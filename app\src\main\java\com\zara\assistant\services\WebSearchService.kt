package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.zara.assistant.data.local.dao.UserLearningDao
import com.zara.assistant.domain.model.SearchCache
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.net.URLEncoder
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Intelligent web search service with caching and learning
 */
@Singleton
class WebSearchService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userLearningDao: UserLearningDao,
    private val okHttpClient: OkHttpClient
) {
    companion object {
        private const val TAG = "WebSearchService"
        private const val GOOGLE_SEARCH_API = "https://www.googleapis.com/customsearch/v1"
        private const val CACHE_EXPIRY_HOURS = 24
        private const val MAX_RESULTS = 5
    }

    private val gson = Gson()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * Search for information with intelligent caching
     */
    suspend fun search(query: String, useCache: Boolean = true): SearchResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "🔍 Searching for: $query")

                // Check cache first if enabled
                if (useCache) {
                    val cachedResult = getCachedResult(query)
                    if (cachedResult != null) {
                        Log.d(TAG, "📋 Using cached result for: $query")
                        updateCacheAccess(cachedResult.id)
                        return@withContext parseSearchResult(cachedResult.results)
                    }
                }

                // Perform web search
                val searchResult = performWebSearch(query)

                // Cache the result
                cacheSearchResult(query, searchResult)

                // Learn from search patterns
                learnFromSearch(query, searchResult)

                searchResult

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error searching for: $query", e)
                SearchResult(
                    query = query,
                    results = emptyList(),
                    success = false,
                    errorMessage = e.message
                )
            }
        }
    }

    /**
     * Get contextual search suggestions based on conversation
     */
    suspend fun getContextualSuggestions(
        conversationContext: String,
        userInterests: List<String>
    ): List<String> {
        return try {
            val suggestions = mutableListOf<String>()
            
            // Analyze conversation for search opportunities
            val topics = extractTopicsFromContext(conversationContext)
            
            // Add topic-based suggestions
            topics.forEach { topic ->
                suggestions.add("Tell me more about $topic")
                suggestions.add("Latest news on $topic")
            }
            
            // Add interest-based suggestions
            userInterests.take(3).forEach { interest ->
                suggestions.add("What's new with $interest")
                suggestions.add("Updates on $interest")
            }
            
            // Get popular searches from cache
            val popularSearches = userLearningDao.getPopularSearches(5)
            popularSearches.forEach { cache: SearchCache ->
                if (!suggestions.contains(cache.query)) {
                    suggestions.add(cache.query)
                }
            }
            
            suggestions.take(10)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting contextual suggestions", e)
            emptyList()
        }
    }

    /**
     * Generate voice-friendly summary from search results
     */
    fun generateVoiceSummary(searchResult: SearchResult): String {
        return try {
            if (!searchResult.success || searchResult.results.isEmpty()) {
                "I couldn't find any information about ${searchResult.query}."
            } else {
                val topResult = searchResult.results.first()
                val summary = StringBuilder()
                
                summary.append("Here's what I found about ${searchResult.query}: ")
                summary.append(topResult.snippet)
                
                if (searchResult.results.size > 1) {
                    summary.append(" I found ${searchResult.results.size} results total.")
                }
                
                summary.toString()
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating voice summary", e)
            "I had trouble processing the search results."
        }
    }

    private suspend fun getCachedResult(query: String): SearchCache? {
        return try {
            val cached = userLearningDao.getCachedSearch(query)
            val expiryTime = System.currentTimeMillis() - (CACHE_EXPIRY_HOURS * 60 * 60 * 1000)
            
            if (cached != null && cached.timestamp > expiryTime) {
                cached
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting cached result", e)
            null
        }
    }

    private suspend fun performWebSearch(query: String): SearchResult {
        return try {
            Log.d(TAG, "🌐 Performing web search for: $query")

            // Use Perplexity API for real-time web search
            val searchResults = searchWithPerplexity(query)

            if (searchResults.isNotEmpty()) {
                SearchResult(
                    query = query,
                    results = searchResults,
                    success = true,
                    timestamp = System.currentTimeMillis()
                )
            } else {
                // Fallback to mock results if Perplexity fails
                Log.w(TAG, "⚠️ Perplexity search failed, using fallback")
                val mockResults = createMockSearchResults(query)
                SearchResult(
                    query = query,
                    results = mockResults,
                    success = true,
                    timestamp = System.currentTimeMillis()
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error performing web search", e)
            throw e
        }
    }

    private suspend fun searchWithPerplexity(query: String): List<SearchResultItem> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "🔍 Searching with Perplexity: $query")

                // Create search request for Perplexity
                val mediaType = "application/json".toMediaType()
                val requestBody = createPerplexitySearchPayload(query).toRequestBody(mediaType)

                val request = okhttp3.Request.Builder()
                    .url("https://api.perplexity.ai/chat/completions")
                    .post(requestBody)
                    .addHeader("Authorization", "Bearer 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")
                    .addHeader("Content-Type", "application/json")
                    .build()

                Log.d(TAG, "📡 Making Perplexity API request on IO thread...")
                val response = okHttpClient.newCall(request).execute()
                Log.d(TAG, "📡 Perplexity response: ${response.code}")

                if (response.isSuccessful) {
                    val responseBody = response.body?.string()
                    Log.d(TAG, "📄 Response body length: ${responseBody?.length ?: 0}")
                    if (responseBody != null) {
                        val results = parsePerplexityResponse(responseBody, query)
                        Log.d(TAG, "✅ Parsed ${results.size} results from Perplexity")
                        results
                    } else {
                        Log.w(TAG, "⚠️ Empty response body from Perplexity")
                        emptyList()
                    }
                } else {
                    Log.e(TAG, "❌ Perplexity API error: ${response.code} - ${response.message}")
                    val errorBody = response.body?.string()
                    Log.e(TAG, "❌ Error response: $errorBody")
                    emptyList()
                }

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error searching with Perplexity", e)
                emptyList()
            }
        }
    }

    private fun createPerplexitySearchPayload(query: String): String {
        return """
        {
            "model": "sonar",
            "messages": [
                {
                    "role": "system",
                    "content": "You are Zara, an intelligent AI assistant. Search the web and provide current, accurate information about the user's query. Give a concise but informative response suitable for voice output. Focus on the most relevant and recent information. NOte : give me summarize news and information max 3 sentence"
                },
                {
                    "role": "user",
                    "content": "$query"
                }
            ],
            "max_tokens": 200,
            "temperature": 0.2,
            "return_citations": true,
            "search_recency_filter": "month",
            "stream": false
        }
        """.trimIndent()
    }

    private fun parsePerplexityResponse(responseBody: String, query: String): List<SearchResultItem> {
        return try {
            val jsonResponse = gson.fromJson(responseBody, com.google.gson.JsonObject::class.java)
            val choices = jsonResponse.getAsJsonArray("choices")

            if (choices != null && choices.size() > 0) {
                val message = choices[0].asJsonObject.getAsJsonObject("message")
                val content = message.get("content").asString

                // Create search result from Perplexity response
                listOf(
                    SearchResultItem(
                        title = "Current Information: $query",
                        url = "https://perplexity.ai/search?q=${URLEncoder.encode(query, "UTF-8")}",
                        snippet = content,
                        source = "Perplexity AI"
                    )
                )
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error parsing Perplexity response", e)
            emptyList()
        }
    }

    private fun createMockSearchResults(query: String): List<SearchResultItem> {
        // Fallback mock results for demonstration
        return listOf(
            SearchResultItem(
                title = "About $query - Wikipedia",
                url = "https://en.wikipedia.org/wiki/${URLEncoder.encode(query, "UTF-8")}",
                snippet = "This is comprehensive information about $query from Wikipedia. It covers the main aspects and provides detailed explanations.",
                source = "Wikipedia"
            ),
            SearchResultItem(
                title = "$query - Latest News",
                url = "https://news.example.com/$query",
                snippet = "Recent developments and news about $query. Stay updated with the latest information and trends.",
                source = "News"
            )
        )
    }

    private suspend fun cacheSearchResult(query: String, result: SearchResult) {
        try {
            val cacheEntry = SearchCache(
                query = query,
                results = gson.toJson(result.results),
                timestamp = System.currentTimeMillis(),
                source = "web_search"
            )
            
            userLearningDao.insertSearchCache(cacheEntry)
            Log.d(TAG, "💾 Cached search result for: $query")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error caching search result", e)
        }
    }

    private suspend fun updateCacheAccess(cacheId: Long) {
        try {
            userLearningDao.updateSearchAccess(cacheId)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating cache access", e)
        }
    }

    private fun parseSearchResult(cachedResults: String): SearchResult {
        return try {
            val results = gson.fromJson(cachedResults, Array<SearchResultItem>::class.java).toList()
            SearchResult(
                query = "",
                results = results,
                success = true,
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error parsing cached results", e)
            SearchResult(query = "", results = emptyList(), success = false)
        }
    }

    private suspend fun learnFromSearch(query: String, result: SearchResult) {
        serviceScope.launch {
            try {
                // Analyze search patterns for learning
                val topics = extractTopicsFromQuery(query)
                
                // Store search patterns for future suggestions
                // This could be expanded to include more sophisticated learning
                Log.d(TAG, "📚 Learning from search: $query (${topics.size} topics identified)")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error learning from search", e)
            }
        }
    }

    private fun extractTopicsFromContext(context: String): List<String> {
        // Simple topic extraction - in production, use NLP libraries
        val keywords = listOf("weather", "news", "music", "food", "travel", "technology", "sports", "health")
        return keywords.filter { context.lowercase().contains(it) }
    }

    private fun extractTopicsFromQuery(query: String): List<String> {
        // Extract meaningful topics from search query
        val words = query.lowercase().split(" ")
        return words.filter { it.length > 3 } // Simple filtering
    }

    /**
     * Get search analytics and insights
     */
    suspend fun getSearchAnalytics(): SearchAnalytics {
        return try {
            val popularSearches = userLearningDao.getPopularSearches(10)
            val totalSearches = popularSearches.sumOf { it.accessCount }
            
            SearchAnalytics(
                totalSearches = totalSearches,
                popularQueries = popularSearches.map { it: SearchCache -> it.query },
                cacheHitRate = calculateCacheHitRate(),
                topTopics = extractTopTopics(popularSearches.map { it: SearchCache -> it.query })
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting search analytics", e)
            SearchAnalytics()
        }
    }

    private fun calculateCacheHitRate(): Float {
        // Calculate cache hit rate based on access patterns
        return 0.75f // Mock value for demonstration
    }

    private fun extractTopTopics(queries: List<String>): List<String> {
        // Extract most common topics from search queries
        val allWords = queries.flatMap { it.split(" ") }
        return allWords.groupingBy { it }.eachCount()
            .toList()
            .sortedByDescending { it.second }
            .take(5)
            .map { it.first }
    }

    fun cleanup() {
        serviceScope.cancel()
    }
}

/**
 * Search result data classes
 */
data class SearchResult(
    val query: String,
    val results: List<SearchResultItem>,
    val success: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val errorMessage: String? = null
)

data class SearchResultItem(
    val title: String,
    val url: String,
    val snippet: String,
    val source: String
)

data class SearchAnalytics(
    val totalSearches: Int = 0,
    val popularQueries: List<String> = emptyList(),
    val cacheHitRate: Float = 0f,
    val topTopics: List<String> = emptyList()
)
