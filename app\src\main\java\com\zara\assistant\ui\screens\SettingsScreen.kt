package com.zara.assistant.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.zara.assistant.R
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard
import com.zara.assistant.ui.viewmodel.SettingsViewModel

/**
 * Settings screen for configuring Zara
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel,
    onNavigateBack: () -> Unit,
    onNavigateToAbout: () -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.settings),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Voice Settings Section
            item {
                SettingsSection(title = stringResource(R.string.voice_settings)) {
                    // Wake Word Settings
                    SettingsItem(
                        title = stringResource(R.string.wake_word_enabled),
                        subtitle = "Say \"Hey Zara\" to activate",
                        trailing = {
                            Switch(
                                checked = uiState.isWakeWordEnabled,
                                onCheckedChange = viewModel::onWakeWordToggle
                            )
                        }
                    )

                    if (uiState.isWakeWordEnabled) {
                        SettingsSliderItem(
                            title = stringResource(R.string.wake_word_sensitivity),
                            value = uiState.wakeWordSensitivity,
                            onValueChange = viewModel::onSensitivityChange,
                            valueRange = 0.1f..1.0f
                        )
                    }

                    SettingsSliderItem(
                        title = stringResource(R.string.speech_rate),
                        value = uiState.speechRate,
                        onValueChange = viewModel::onSpeechRateChange,
                        valueRange = 0.5f..2.0f
                    )

                    SettingsSliderItem(
                        title = stringResource(R.string.speech_pitch),
                        value = uiState.speechPitch,
                        onValueChange = viewModel::onSpeechPitchChange,
                        valueRange = 0.5f..2.0f
                    )

                    SettingsItem(
                        title = stringResource(R.string.auto_listen_after_response),
                        subtitle = "Continue listening after Zara responds",
                        trailing = {
                            Switch(
                                checked = uiState.autoListen,
                                onCheckedChange = viewModel::onAutoListenToggle
                            )
                        }
                    )
                }
            }

            // AI Settings Section
            item {
                SettingsSection(title = stringResource(R.string.ai_settings)) {
                    SettingsDropdownItem(
                        title = stringResource(R.string.ai_personality),
                        selectedValue = uiState.aiPersonality.displayName,
                        options = Constants.AIPersonality.values().map { it.displayName },
                        onSelectionChange = { selectedName ->
                            val personality = Constants.AIPersonality.values()
                                .find { it.displayName == selectedName }
                            personality?.let { viewModel.onPersonalityChange(it) }
                        }
                    )

                    SettingsDropdownItem(
                        title = stringResource(R.string.ai_response_style),
                        selectedValue = uiState.aiResponseStyle.displayName,
                        options = Constants.AIResponseStyle.values().map { it.displayName },
                        onSelectionChange = { selectedName ->
                            val style = Constants.AIResponseStyle.values()
                                .find { it.displayName == selectedName }
                            style?.let { viewModel.onResponseStyleChange(it) }
                        }
                    )
                }
            }

            // Privacy Settings Section
            item {
                SettingsSection(title = stringResource(R.string.privacy_settings)) {
                    SettingsItem(
                        title = stringResource(R.string.conversation_history),
                        subtitle = "Save conversation history for context",
                        trailing = {
                            Switch(
                                checked = uiState.conversationHistoryEnabled,
                                onCheckedChange = viewModel::onConversationHistoryToggle
                            )
                        }
                    )

                    SettingsItem(
                        title = stringResource(R.string.voice_data_storage),
                        subtitle = "Store voice recordings for improvement",
                        trailing = {
                            Switch(
                                checked = uiState.voiceDataStorageEnabled,
                                onCheckedChange = viewModel::onVoiceDataStorageToggle
                            )
                        }
                    )

                    SettingsItem(
                        title = stringResource(R.string.analytics_enabled),
                        subtitle = "Share anonymous usage data",
                        trailing = {
                            Switch(
                                checked = uiState.analyticsEnabled,
                                onCheckedChange = viewModel::onAnalyticsToggle
                            )
                        }
                    )
                }
            }

            // Data Management Section
            item {
                SettingsSection(title = "Data Management") {
                    NeumorphismButton(
                        text = stringResource(R.string.clear_data),
                        onClick = viewModel::onClearData,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    NeumorphismButton(
                        text = stringResource(R.string.export_data),
                        onClick = viewModel::onExportData,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            // About Section
            item {
                SettingsSection(title = stringResource(R.string.about)) {
                    NeumorphismButton(
                        text = "About Zara",
                        onClick = onNavigateToAbout,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 4.dp, vertical = 8.dp)
        )

        NeumorphismCard {
            Column {
                content()
            }
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    subtitle: String? = null,
    trailing: @Composable (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            subtitle?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        trailing?.invoke()
    }
}

@Composable
private fun SettingsSliderItem(
    title: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = String.format("%.1f", value),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun SettingsDropdownItem(
    title: String,
    selectedValue: String,
    options: List<String>,
    onSelectionChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    // For now, just show the selected value
    // In a full implementation, this would be a dropdown menu
    SettingsItem(
        title = title,
        subtitle = selectedValue,
        modifier = modifier
    )
}
