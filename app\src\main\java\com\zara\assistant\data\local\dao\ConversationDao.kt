package com.zara.assistant.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.zara.assistant.data.local.database.entities.ConversationEntity
import com.zara.assistant.data.local.database.entities.ConversationMessageEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for conversation operations
 */
@Dao
interface ConversationDao {
    
    @Query("SELECT * FROM conversations ORDER BY startTime DESC")
    fun getAllConversations(): Flow<List<ConversationEntity>>
    
    @Query("SELECT * FROM conversations WHERE id = :id")
    suspend fun getConversationById(id: String): ConversationEntity?
    
    @Query("SELECT * FROM conversations WHERE isActive = 1 LIMIT 1")
    suspend fun getActiveConversation(): ConversationEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConversation(conversation: ConversationEntity)
    
    @Update
    suspend fun updateConversation(conversation: ConversationEntity)
    
    @Delete
    suspend fun deleteConversation(conversation: ConversationEntity)
    
    @Query("DELETE FROM conversations WHERE id = :id")
    suspend fun deleteConversationById(id: String)
    
    @Query("DELETE FROM conversations")
    suspend fun deleteAllConversations()
    
    // Message operations
    @Query("SELECT * FROM conversation_messages WHERE conversationId = :conversationId ORDER BY timestamp ASC")
    fun getMessagesForConversation(conversationId: String): Flow<List<ConversationMessageEntity>>
    
    @Query("SELECT * FROM conversation_messages WHERE conversationId = :conversationId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentMessages(conversationId: String, limit: Int): List<ConversationMessageEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: ConversationMessageEntity)
    
    @Delete
    suspend fun deleteMessage(message: ConversationMessageEntity)
    
    @Query("DELETE FROM conversation_messages WHERE id = :messageId")
    suspend fun deleteMessageById(messageId: String)
    
    @Query("SELECT COUNT(*) FROM conversation_messages WHERE conversationId = :conversationId")
    suspend fun getMessageCount(conversationId: String): Int
    
    @Query("SELECT * FROM conversation_messages WHERE content LIKE '%' || :query || '%'")
    suspend fun searchMessages(query: String): List<ConversationMessageEntity>
    
    @Query("SELECT * FROM conversations WHERE startTime BETWEEN :startDate AND :endDate ORDER BY startTime DESC")
    suspend fun getConversationsByDateRange(startDate: Long, endDate: Long): List<ConversationEntity>
}
