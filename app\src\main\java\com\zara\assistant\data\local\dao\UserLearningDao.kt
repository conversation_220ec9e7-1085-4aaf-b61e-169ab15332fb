package com.zara.assistant.data.local.dao

import androidx.room.*
import com.zara.assistant.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for user learning data
 */
@Dao
interface UserLearningDao {
    
    // User Interactions
    @Insert
    suspend fun insertInteraction(interaction: UserInteraction): Long
    
    @Query("SELECT * FROM user_interactions ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentInteractions(limit: Int = 100): List<UserInteraction>
    
    @Query("SELECT * FROM user_interactions WHERE timestamp >= :startTime AND timestamp <= :endTime")
    suspend fun getInteractionsByTimeRange(startTime: Long, endTime: Long): List<UserInteraction>
    
    @Query("SELECT * FROM user_interactions WHERE timeOfDay = :hour")
    suspend fun getInteractionsByHour(hour: Int): List<UserInteraction>
    
    @Query("SELECT * FROM user_interactions WHERE dayOfWeek = :dayOfWeek")
    suspend fun getInteractionsByDayOfWeek(dayOfWeek: Int): List<UserInteraction>
    
    @Query("SELECT command, COUNT(*) as frequency FROM user_interactions WHERE success = 1 GROUP BY command ORDER BY frequency DESC LIMIT :limit")
    suspend fun getMostFrequentCommands(limit: Int = 10): List<CommandFrequency>
    
    @Query("DELETE FROM user_interactions WHERE timestamp < :cutoffTime")
    suspend fun deleteOldInteractions(cutoffTime: Long): Int
    
    // User Patterns
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPattern(pattern: UserPattern): Long
    
    @Update
    suspend fun updatePattern(pattern: UserPattern)
    
    @Query("SELECT * FROM user_patterns WHERE isActive = 1 ORDER BY confidence DESC")
    suspend fun getActivePatterns(): List<UserPattern>
    
    @Query("SELECT * FROM user_patterns WHERE patternType = :type AND isActive = 1")
    suspend fun getPatternsByType(type: String): List<UserPattern>
    
    @Query("SELECT * FROM user_patterns WHERE confidence >= :minConfidence AND isActive = 1")
    suspend fun getHighConfidencePatterns(minConfidence: Float): List<UserPattern>
    
    @Query("UPDATE user_patterns SET isActive = 0 WHERE id = :patternId")
    suspend fun deactivatePattern(patternId: Long)
    
    // User Preferences
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPreference(preference: UserPreference): Long
    
    @Query("SELECT * FROM user_preferences WHERE preferenceKey = :key")
    suspend fun getPreference(key: String): UserPreference?
    
    @Query("SELECT * FROM user_preferences ORDER BY confidence DESC")
    suspend fun getAllPreferences(): List<UserPreference>
    
    @Query("DELETE FROM user_preferences WHERE preferenceKey = :key")
    suspend fun deletePreference(key: String)
    
    // User Profile Management
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserProfile(profile: UserProfile): Long
    
    @Query("SELECT * FROM user_profile WHERE id = :userId")
    suspend fun getUserProfile(userId: String = "default_user"): UserProfile?
    
    @Update
    suspend fun updateUserProfile(profile: UserProfile)
    
    // Conversation History
    @Insert
    suspend fun insertConversationHistory(conversation: ConversationHistory): Long
    
    @Query("SELECT * FROM conversation_history ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentConversations(limit: Int = 50): List<ConversationHistory>
    
    @Query("SELECT * FROM conversation_history WHERE sessionId = :sessionId ORDER BY timestamp")
    suspend fun getConversationsBySession(sessionId: String): List<ConversationHistory>
    
    @Query("SELECT * FROM conversation_history WHERE topics LIKE '%' || :topic || '%'")
    suspend fun getConversationsByTopic(topic: String): List<ConversationHistory>
    
    // Search Cache Management
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearchCache(cache: SearchCache): Long
    
    @Query("SELECT * FROM search_cache WHERE query = :query ORDER BY timestamp DESC LIMIT 1")
    suspend fun getCachedSearch(query: String): SearchCache?
    
    @Query("SELECT * FROM search_cache ORDER BY accessCount DESC, timestamp DESC LIMIT :limit")
    suspend fun getPopularSearches(limit: Int = 10): List<SearchCache>
    
    @Query("UPDATE search_cache SET accessCount = accessCount + 1, lastAccessed = :timestamp WHERE id = :id")
    suspend fun updateSearchAccess(id: Long, timestamp: Long = System.currentTimeMillis())
    
    // User Favorites
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserFavorite(favorite: UserFavorite): Long
    
    @Query("SELECT * FROM user_favorites WHERE category = :category ORDER BY confidence DESC")
    suspend fun getFavoritesByCategory(category: String): List<UserFavorite>
    
    @Query("SELECT * FROM user_favorites ORDER BY confidence DESC LIMIT :limit")
    suspend fun getTopFavorites(limit: Int = 20): List<UserFavorite>
    
    // Behavioral Patterns
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBehavioralPattern(pattern: BehavioralPattern): Long
    
    @Query("SELECT * FROM behavioral_patterns WHERE isActive = 1 ORDER BY confidence DESC")
    suspend fun getActiveBehavioralPatterns(): List<BehavioralPattern>
    
    @Query("SELECT * FROM behavioral_patterns WHERE patternType = :type AND isActive = 1")
    suspend fun getBehavioralPatternsByType(type: String): List<BehavioralPattern>
    
    // ML Model Data
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMLModelData(modelData: MLModelData): Long
    
    @Query("SELECT * FROM ml_model_data WHERE modelName = :modelName AND isActive = 1")
    suspend fun getMLModelData(modelName: String): MLModelData?
    
    @Query("SELECT * FROM ml_model_data WHERE isActive = 1")
    suspend fun getAllActiveModels(): List<MLModelData>
    
    // Contextual Data
    @Insert
    suspend fun insertContextualData(context: ContextualData): Long
    
    @Query("SELECT * FROM contextual_data ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentContextualData(limit: Int = 100): List<ContextualData>
    
    // Analytics queries
    @Query("SELECT COUNT(*) FROM user_interactions WHERE timestamp >= :startTime")
    suspend fun getInteractionCountSince(startTime: Long): Int
    
    @Query("SELECT AVG(executionTimeMs) FROM user_interactions WHERE success = 1 AND timestamp >= :startTime")
    suspend fun getAverageExecutionTime(startTime: Long): Double?
    
    @Query("SELECT (COUNT(CASE WHEN success = 1 THEN 1 END) * 100.0 / COUNT(*)) as successRate FROM user_interactions WHERE timestamp >= :startTime")
    suspend fun getSuccessRate(startTime: Long): Double?
}

/**
 * Data class for command frequency results
 */
data class CommandFrequency(
    val command: String,
    val frequency: Int
)
