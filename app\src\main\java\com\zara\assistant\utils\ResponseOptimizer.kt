package com.zara.assistant.utils

import com.zara.assistant.core.Constants

/**
 * Utility for optimizing AI responses for different contexts
 */
object ResponseOptimizer {
    
    /**
     * Optimize response for voice output
     */
    fun optimizeForVoice(
        response: String,
        responseStyle: Constants.AIResponseStyle = Constants.AIResponseStyle.CONVERSATIONAL
    ): String {
        var optimized = response.trim()
        
        when (responseStyle) {
            Constants.AIResponseStyle.BRIEF -> {
                // Keep only the first sentence for brief responses
                optimized = optimized.split(". ", "! ", "? ")
                    .firstOrNull()?.trim() ?: optimized
            }
            
            Constants.AIResponseStyle.CONVERSATIONAL -> {
                // Keep 1-2 sentences for conversational responses
                val sentences = optimized.split(". ", "! ", "? ")
                    .filter { it.isNotBlank() }
                    .take(2)
                
                optimized = sentences.joinToString(". ") { sentence ->
                    val trimmed = sentence.trim()
                    if (!trimmed.endsWith(".") && !trimmed.endsWith("!") && !trimmed.endsWith("?")) {
                        "$trimmed."
                    } else {
                        trimmed
                    }
                }
            }
            
            Constants.AIResponseStyle.DETAILED -> {
                // Keep more content but still optimize for voice
                val sentences = optimized.split(". ", "! ", "? ")
                    .filter { it.isNotBlank() }
                    .take(4)
                
                optimized = sentences.joinToString(". ") { sentence ->
                    val trimmed = sentence.trim()
                    if (!trimmed.endsWith(".") && !trimmed.endsWith("!") && !trimmed.endsWith("?")) {
                        "$trimmed."
                    } else {
                        trimmed
                    }
                }
            }
        }
        
        // Clean up for TTS
        optimized = cleanForTTS(optimized)
        
        return optimized
    }
    
    /**
     * Clean text for Text-to-Speech output
     */
    private fun cleanForTTS(text: String): String {
        return text
            .replace("\\n", " ") // Remove line breaks
            .replace("\\t", " ") // Remove tabs
            .replace("  +".toRegex(), " ") // Remove multiple spaces
            .replace("*", "") // Remove markdown bold
            .replace("#", "") // Remove markdown headers
            .replace("_", "") // Remove markdown italic
            .replace("`", "") // Remove code formatting
            .replace("\\[.*?\\]".toRegex(), "") // Remove markdown links
            .replace("\\(.*?\\)".toRegex(), "") // Remove parenthetical content for voice
            .trim()
            .let { cleaned ->
                // Ensure proper ending punctuation
                if (!cleaned.endsWith(".") && !cleaned.endsWith("!") && !cleaned.endsWith("?")) {
                    "$cleaned."
                } else {
                    cleaned
                }
            }
    }
    
    /**
     * Get optimal response length for different contexts
     */
    fun getOptimalLength(responseStyle: Constants.AIResponseStyle): Int {
        return when (responseStyle) {
            Constants.AIResponseStyle.BRIEF -> 30 // ~30 tokens for brief
            Constants.AIResponseStyle.CONVERSATIONAL -> 50 // ~50 tokens for conversational
            Constants.AIResponseStyle.DETAILED -> 100 // ~100 tokens for detailed
        }
    }
    
    /**
     * Check if response is appropriate for voice output
     */
    fun isVoiceFriendly(response: String): Boolean {
        val wordCount = response.split("\\s+".toRegex()).size
        val sentenceCount = response.split("[.!?]".toRegex()).size
        
        return when {
            wordCount > 100 -> false // Too long for voice
            sentenceCount > 5 -> false // Too many sentences
            response.contains("```") -> false // Contains code blocks
            response.contains("http") -> false // Contains URLs
            response.length > 500 -> false // Too many characters
            else -> true
        }
    }
    
    /**
     * Generate voice-optimized prompts
     */
    fun createVoicePrompt(
        basePrompt: String,
        responseStyle: Constants.AIResponseStyle
    ): String {
        val voiceInstructions = when (responseStyle) {
            Constants.AIResponseStyle.BRIEF -> 
                "Give a very short answer in 1 sentence. This will be spoken aloud."
            
            Constants.AIResponseStyle.CONVERSATIONAL -> 
                "Give a natural, conversational answer in 1-2 sentences. Keep it friendly and human-like. This will be spoken aloud."
            
            Constants.AIResponseStyle.DETAILED -> 
                "Give a comprehensive but voice-friendly answer in 2-4 sentences. This will be spoken aloud, so avoid lists or complex formatting."
        }
        
        return "$basePrompt\n\nImportant: $voiceInstructions"
    }
}
