package com.zara.assistant.data.repository

import com.zara.assistant.core.Constants
import com.zara.assistant.data.local.preferences.PreferencesManager
import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.domain.repository.SettingsRepository
import com.zara.assistant.domain.repository.ThemeMode
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of SettingsRepository
 */
@Singleton
class SettingsRepositoryImpl @Inject constructor(
    private val preferencesManager: PreferencesManager
) : SettingsRepository {

    override suspend fun getVoiceSettings(): VoiceSettings {
        return preferencesManager.getVoiceSettings().first()
    }

    override suspend fun updateVoiceSettings(settings: VoiceSettings): Result<Unit> {
        return try {
            preferencesManager.setWakeWordEnabled(settings.isWakeWordEnabled)
            preferencesManager.setWakeWordSensitivity(settings.wakeWordSensitivity)
            preferencesManager.setSpeechRate(settings.speechRate)
            preferencesManager.setSpeechPitch(settings.speechPitch)
            preferencesManager.setVoiceLanguage(settings.language)
            preferencesManager.setAutoListen(settings.autoListenAfterResponse)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun observeVoiceSettings(): Flow<VoiceSettings> {
        return preferencesManager.getVoiceSettings()
    }

    override suspend fun isWakeWordEnabled(): Boolean {
        return preferencesManager.isWakeWordEnabled().first()
    }

    override suspend fun setWakeWordEnabled(enabled: Boolean): Result<Unit> {
        return try {
            preferencesManager.setWakeWordEnabled(enabled)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getWakeWordSensitivity(): Float {
        return preferencesManager.getWakeWordSensitivity().first()
    }

    override suspend fun setWakeWordSensitivity(sensitivity: Float): Result<Unit> {
        return try {
            preferencesManager.setWakeWordSensitivity(sensitivity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getAIPersonality(): Constants.AIPersonality {
        return preferencesManager.getAIPersonality().first()
    }

    override suspend fun setAIPersonality(personality: Constants.AIPersonality): Result<Unit> {
        return try {
            preferencesManager.setAIPersonality(personality)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getAIResponseStyle(): Constants.AIResponseStyle {
        return preferencesManager.getAIResponseStyle().first()
    }

    override suspend fun setAIResponseStyle(style: Constants.AIResponseStyle): Result<Unit> {
        return try {
            preferencesManager.setAIResponseStyle(style)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isConversationHistoryEnabled(): Boolean {
        return preferencesManager.isConversationHistoryEnabled().first()
    }

    override suspend fun setConversationHistoryEnabled(enabled: Boolean): Result<Unit> {
        return try {
            preferencesManager.setConversationHistoryEnabled(enabled)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isAnalyticsEnabled(): Boolean {
        return preferencesManager.isAnalyticsEnabled().first()
    }

    override suspend fun setAnalyticsEnabled(enabled: Boolean): Result<Unit> {
        return try {
            preferencesManager.setAnalyticsEnabled(enabled)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isVoiceDataStorageEnabled(): Boolean {
        // For now, link this to conversation history
        return isConversationHistoryEnabled()
    }

    override suspend fun setVoiceDataStorageEnabled(enabled: Boolean): Result<Unit> {
        // For now, link this to conversation history
        return setConversationHistoryEnabled(enabled)
    }

    override suspend fun isAccessibilityServiceEnabled(): Boolean {
        return preferencesManager.isAccessibilityServiceEnabled().first()
    }

    override suspend fun setAccessibilityServiceEnabled(enabled: Boolean): Result<Unit> {
        return try {
            preferencesManager.setAccessibilityServiceEnabled(enabled)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isNotificationAccessEnabled(): Boolean {
        return preferencesManager.isNotificationAccessEnabled().first()
    }

    override suspend fun setNotificationAccessEnabled(enabled: Boolean): Result<Unit> {
        return try {
            preferencesManager.setNotificationAccessEnabled(enabled)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getVoiceLanguage(): String {
        return preferencesManager.getVoiceLanguage().first()
    }

    override suspend fun setVoiceLanguage(language: String): Result<Unit> {
        return try {
            preferencesManager.setVoiceLanguage(language)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getSupportedLanguages(): List<String> {
        return listOf(
            "en-US", "en-GB", "en-AU", "en-CA",
            "es-ES", "es-MX", "fr-FR", "fr-CA",
            "de-DE", "it-IT", "pt-BR", "pt-PT",
            "ja-JP", "ko-KR", "zh-CN", "zh-TW",
            "ru-RU", "ar-SA", "hi-IN", "nl-NL"
        )
    }

    override suspend fun isFirstLaunch(): Boolean {
        return preferencesManager.isFirstLaunch().first()
    }

    override suspend fun setFirstLaunchCompleted(): Result<Unit> {
        return try {
            preferencesManager.setFirstLaunchCompleted()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getThemeMode(): ThemeMode {
        return preferencesManager.getThemeMode().first()
    }

    override suspend fun setThemeMode(mode: ThemeMode): Result<Unit> {
        return try {
            preferencesManager.setThemeMode(mode)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun exportSettings(): Result<String> {
        return try {
            val preferences = preferencesManager.exportPreferences()
            val json = com.google.gson.Gson().toJson(preferences)
            Result.success(json)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun importSettings(data: String): Result<Unit> {
        return try {
            // Parse JSON and restore settings
            // This would require implementing import logic
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun resetToDefaults(): Result<Unit> {
        return try {
            preferencesManager.clearAllPreferences()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun areNotificationsEnabled(): Boolean {
        // This would check system notification settings
        return true
    }

    override suspend fun setNotificationsEnabled(enabled: Boolean): Result<Unit> {
        // This would typically open system settings
        return Result.success(Unit)
    }

    override suspend fun getNotificationChannels(): List<String> {
        return listOf(
            "wake_word_channel",
            "voice_processing_channel",
            "ai_response_channel",
            "system_control_channel",
            "error_channel"
        )
    }

    override suspend fun isChannelEnabled(channelId: String): Boolean {
        // This would check if specific notification channel is enabled
        return true
    }

    override suspend fun setChannelEnabled(channelId: String, enabled: Boolean): Result<Unit> {
        // This would enable/disable specific notification channel
        return Result.success(Unit)
    }
}
