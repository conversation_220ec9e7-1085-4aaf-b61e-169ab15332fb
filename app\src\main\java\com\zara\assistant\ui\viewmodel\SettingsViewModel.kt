package com.zara.assistant.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.domain.repository.SettingsRepository
import com.zara.assistant.domain.repository.ThemeMode
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the settings screen
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsRepository: SettingsRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        observeSettings()
    }

    private fun observeSettings() {
        viewModelScope.launch {
            combine(
                settingsRepository.observeVoiceSettings(),
                flowOf(settingsRepository.getAIPersonality()),
                flowOf(settingsRepository.getAIResponseStyle()),
                flowOf(settingsRepository.isConversationHistoryEnabled()),
                flowOf(settingsRepository.isVoiceDataStorageEnabled()),
                flowOf(settingsRepository.isAnalyticsEnabled()),
                flowOf(settingsRepository.getThemeMode())
            ) { flows ->
                val voiceSettings = flows[0] as VoiceSettings
                val personality = flows[1] as Constants.AIPersonality
                val responseStyle = flows[2] as Constants.AIResponseStyle
                val conversationHistory = flows[3] as Boolean
                val voiceDataStorage = flows[4] as Boolean
                val analytics = flows[5] as Boolean
                val themeMode = flows[6] as ThemeMode

                _uiState.value = _uiState.value.copy(
                    isWakeWordEnabled = voiceSettings.isWakeWordEnabled,
                    wakeWordSensitivity = voiceSettings.wakeWordSensitivity,
                    speechRate = voiceSettings.speechRate,
                    speechPitch = voiceSettings.speechPitch,
                    autoListen = voiceSettings.autoListenAfterResponse,
                    aiPersonality = personality,
                    aiResponseStyle = responseStyle,
                    conversationHistoryEnabled = conversationHistory,
                    voiceDataStorageEnabled = voiceDataStorage,
                    analyticsEnabled = analytics,
                    themeMode = themeMode
                )
            }
        }
    }

    fun onWakeWordToggle(enabled: Boolean) {
        viewModelScope.launch {
            settingsRepository.setWakeWordEnabled(enabled)
        }
    }

    fun onSensitivityChange(sensitivity: Float) {
        viewModelScope.launch {
            settingsRepository.setWakeWordSensitivity(sensitivity)
        }
    }

    fun onSpeechRateChange(rate: Float) {
        viewModelScope.launch {
            val currentSettings = settingsRepository.getVoiceSettings()
            val updatedSettings = currentSettings.copy(speechRate = rate)
            settingsRepository.updateVoiceSettings(updatedSettings)
        }
    }

    fun onSpeechPitchChange(pitch: Float) {
        viewModelScope.launch {
            val currentSettings = settingsRepository.getVoiceSettings()
            val updatedSettings = currentSettings.copy(speechPitch = pitch)
            settingsRepository.updateVoiceSettings(updatedSettings)
        }
    }

    fun onAutoListenToggle(enabled: Boolean) {
        viewModelScope.launch {
            val currentSettings = settingsRepository.getVoiceSettings()
            val updatedSettings = currentSettings.copy(autoListenAfterResponse = enabled)
            settingsRepository.updateVoiceSettings(updatedSettings)
        }
    }

    fun onPersonalityChange(personality: Constants.AIPersonality) {
        viewModelScope.launch {
            settingsRepository.setAIPersonality(personality)
        }
    }

    fun onResponseStyleChange(style: Constants.AIResponseStyle) {
        viewModelScope.launch {
            settingsRepository.setAIResponseStyle(style)
        }
    }

    fun onConversationHistoryToggle(enabled: Boolean) {
        viewModelScope.launch {
            settingsRepository.setConversationHistoryEnabled(enabled)
        }
    }

    fun onVoiceDataStorageToggle(enabled: Boolean) {
        viewModelScope.launch {
            settingsRepository.setVoiceDataStorageEnabled(enabled)
        }
    }

    fun onAnalyticsToggle(enabled: Boolean) {
        viewModelScope.launch {
            settingsRepository.setAnalyticsEnabled(enabled)
        }
    }

    fun onThemeModeChange(mode: ThemeMode) {
        viewModelScope.launch {
            settingsRepository.setThemeMode(mode)
        }
    }

    fun onClearData() {
        viewModelScope.launch {
            try {
                // Clear conversation history, cache, etc.
                _uiState.value = _uiState.value.copy(
                    message = "Data cleared successfully"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to clear data: ${e.message}"
                )
            }
        }
    }

    fun onExportData() {
        viewModelScope.launch {
            try {
                val result = settingsRepository.exportSettings()
                result.onSuccess { data ->
                    _uiState.value = _uiState.value.copy(
                        message = "Data exported successfully"
                    )
                }.onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "Failed to export data: ${error.message}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to export data: ${e.message}"
                )
            }
        }
    }

    fun dismissMessage() {
        _uiState.value = _uiState.value.copy(
            message = null,
            errorMessage = null
        )
    }
}

/**
 * UI state for the settings screen
 */
data class SettingsUiState(
    val isWakeWordEnabled: Boolean = true,
    val wakeWordSensitivity: Float = 0.5f,
    val speechRate: Float = 1.0f,
    val speechPitch: Float = 1.0f,
    val autoListen: Boolean = false,
    val aiPersonality: Constants.AIPersonality = Constants.AIPersonality.FRIENDLY,
    val aiResponseStyle: Constants.AIResponseStyle = Constants.AIResponseStyle.CONVERSATIONAL,
    val conversationHistoryEnabled: Boolean = true,
    val voiceDataStorageEnabled: Boolean = false,
    val analyticsEnabled: Boolean = false,
    val themeMode: ThemeMode = ThemeMode.SYSTEM,
    val message: String? = null,
    val errorMessage: String? = null
)
