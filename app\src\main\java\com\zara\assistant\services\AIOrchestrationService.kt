package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.domain.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Central orchestration service that coordinates all AI services
 */
@Singleton
class AIOrchestrationService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val personalMemoryService: PersonalMemoryService,
    private val mlPersonalizationService: MLPersonalizationService,
    private val webSearchService: WebSearchService
) {
    companion object {
        private const val TAG = "AIOrchestrationService"
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var isInitialized = false

    /**
     * Initialize all AI services
     */
    suspend fun initialize() {
        if (isInitialized) return
        
        try {
            Log.d(TAG, "🚀 Initializing AI Orchestration Service...")
            
            // Initialize services in order
            personalMemoryService.initialize()
            mlPersonalizationService.initializeModels()
            
            isInitialized = true
            Log.d(TAG, "✅ AI Orchestration Service initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error initializing AI Orchestration Service", e)
        }
    }

    /**
     * Process user input with full AI pipeline
     */
    suspend fun processUserInput(
        userInput: String,
        conversationContext: String,
        sessionId: String
    ): AIResponse {
        return try {
            Log.d(TAG, "🧠 Processing user input: $userInput")
            
            // Step 1: Extract and store personal information
            val context = getCurrentContext()
            personalMemoryService.extractAndStorePersonalInfo(userInput, context)
            
            // Step 2: Determine if this is an information request
            val isInformationRequest = isInformationRequest(userInput)
            
            // Step 3: Generate response based on type
            val response = if (isInformationRequest) {
                handleInformationRequest(userInput, conversationContext)
            } else {
                handleGeneralConversation(userInput, conversationContext)
            }
            
            // Step 4: Personalize the response
            val personalizedResponse = personalizeResponse(response, userInput)
            
            // Step 5: Store conversation for learning
            personalMemoryService.storeConversation(
                sessionId = sessionId,
                userInput = userInput,
                zaraResponse = personalizedResponse.text,
                conversationType = response.type,
                success = true
            )

            // Log interaction for learning service
            logInteractionForLearning(userInput, personalizedResponse.text, true)
            
            // Step 6: Generate proactive suggestions
            val suggestions = generateProactiveSuggestions(userInput, personalizedResponse.text)
            
            AIResponse(
                text = personalizedResponse.text,
                type = response.type,
                confidence = personalizedResponse.confidence,
                suggestions = suggestions,
                hasSearchResults = response.hasSearchResults,
                searchResults = response.searchResults
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing user input", e)
            AIResponse(
                text = "I'm having trouble processing that right now. Could you try again?",
                type = "error",
                confidence = 0.0f
            )
        }
    }

    /**
     * Generate personalized greeting
     */
    suspend fun generateGreeting(): String {
        return try {
            personalMemoryService.generatePersonalizedGreeting()
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating greeting", e)
            "Hello! How can I help you today?"
        }
    }

    /**
     * Get proactive suggestions based on current context
     */
    suspend fun getProactiveSuggestions(): List<ProactiveSuggestion> {
        return try {
            personalMemoryService.generateProactiveSuggestions()
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting proactive suggestions", e)
            emptyList()
        }
    }

    /**
     * Handle information requests with web search
     */
    private suspend fun handleInformationRequest(
        userInput: String,
        conversationContext: String
    ): BaseAIResponse {
        return try {
            // Extract search query from user input
            val searchQuery = extractSearchQuery(userInput)
            Log.d(TAG, "🔍 Extracted search query: '$searchQuery' from input: '$userInput'")

            // Perform web search
            Log.d(TAG, "🌐 Calling WebSearchService.search() for: '$searchQuery'")
            val searchResult = webSearchService.search(searchQuery)
            Log.d(TAG, "📊 Search result: success=${searchResult.success}, results=${searchResult.results.size}")

            if (searchResult.success && searchResult.results.isNotEmpty()) {
                // Generate voice-friendly summary
                Log.d(TAG, "✅ Generating voice summary for search results")
                val summary = webSearchService.generateVoiceSummary(searchResult)
                Log.d(TAG, "🗣️ Generated summary: $summary")

                BaseAIResponse(
                    text = summary,
                    type = "information",
                    confidence = 0.9f,
                    hasSearchResults = true,
                    searchResults = searchResult.results
                )
            } else {
                Log.w(TAG, "⚠️ No search results found for: '$searchQuery'")
                BaseAIResponse(
                    text = "I couldn't find current information about $searchQuery. Let me try a different approach.",
                    type = "information",
                    confidence = 0.3f
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error handling information request", e)
            BaseAIResponse(
                text = "I'm having trouble searching for that information right now.",
                type = "error",
                confidence = 0.0f
            )
        }
    }

    /**
     * Handle general conversation
     */
    private suspend fun handleGeneralConversation(
        userInput: String,
        conversationContext: String
    ): BaseAIResponse {
        return try {
            // Recall relevant memories
            val relevantMemories = personalMemoryService.recallRelevantInfo(extractTopics(userInput))
            
            // Generate contextual response
            val response = generateContextualResponse(userInput, relevantMemories)
            
            BaseAIResponse(
                text = response,
                type = "conversation",
                confidence = 0.8f
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error handling general conversation", e)
            BaseAIResponse(
                text = "I understand. How else can I help you?",
                type = "conversation",
                confidence = 0.5f
            )
        }
    }

    /**
     * Personalize response using ML
     */
    private suspend fun personalizeResponse(
        baseResponse: BaseAIResponse,
        userInput: String
    ): PersonalizedResponse {
        return try {
            // Get user profile and conversation history
            val userProfile = personalMemoryService.userProfile
            val conversationHistory = emptyList<ConversationHistory>() // Get from DAO if needed
            
            // Use ML to personalize response
            val personalizedText = mlPersonalizationService.personalizeResponse(
                baseResponse.text,
                userProfile,
                conversationHistory
            )
            
            PersonalizedResponse(
                text = personalizedText,
                confidence = baseResponse.confidence
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error personalizing response", e)
            PersonalizedResponse(
                text = baseResponse.text,
                confidence = baseResponse.confidence
            )
        }
    }

    /**
     * Generate proactive suggestions based on conversation
     */
    private suspend fun generateProactiveSuggestions(
        userInput: String,
        response: String
    ): List<ProactiveSuggestion> {
        return try {
            val context = getCurrentContext()
            val recentActions = listOf(userInput) // Could be expanded
            
            // Get ML-based predictions
            val predictions = mlPersonalizationService.predictNextAction(context, recentActions)
            
            // Convert predictions to suggestions
            predictions.take(3).map { prediction ->
                ProactiveSuggestion(
                    title = "You might want to ${prediction.action}",
                    description = prediction.reasoning,
                    command = prediction.action,
                    confidence = prediction.confidence,
                    basedOnPattern = "ML prediction"
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating proactive suggestions", e)
            emptyList()
        }
    }

    private fun isInformationRequest(input: String): Boolean {
        val informationKeywords = listOf(
            "what is", "tell me about", "explain", "how does", "why does",
            "when did", "where is", "who is", "search for", "search", "find information", "find",
            "latest news", "news", "weather", "define", "look up", "how to"
        )
        val lowerInput = input.lowercase()

        // Check for explicit information keywords
        if (informationKeywords.any { lowerInput.contains(it) }) {
            return true
        }

        // If we reach here from INFORMATION_REQUEST conversation type, treat as information request
        // This handles cases like "artificial intelligence" after "search about AI"
        return true // Since this is called from handleInformationRequestWithAI, it's already classified as info request
    }

    private fun extractSearchQuery(input: String): String {
        val lowerInput = input.lowercase()

        // Enhanced patterns for search query extraction
        val patterns = listOf(
            "tell me about (.+)".toRegex(),
            "what is (.+)".toRegex(),
            "search for (.+)".toRegex(),
            "search (.+)".toRegex(),
            "find information about (.+)".toRegex(),
            "find (.+)".toRegex(),
            "look up (.+)".toRegex(),
            "who is (.+)".toRegex(),
            "where is (.+)".toRegex(),
            "when is (.+)".toRegex(),
            "how to (.+)".toRegex()
        )

        patterns.forEach { pattern ->
            pattern.find(lowerInput)?.let { match ->
                return match.groupValues[1].trim()
            }
        }

        // Special handling for news queries
        if (lowerInput.contains("news")) {
            return when {
                lowerInput.contains("latest news") -> "latest news"
                lowerInput.contains("current news") -> "current news"
                else -> "news"
            }
        }

        return input // Fallback to full input
    }

    private fun extractTopics(input: String): String {
        // Extract main topic from input
        val words = input.split(" ").filter { it.length > 3 }
        return words.firstOrNull() ?: input
    }

    private fun generateContextualResponse(
        userInput: String,
        memories: List<MemoryItem>
    ): String {
        // Generate response based on memories and context
        return when {
            memories.isNotEmpty() -> {
                val relevantMemory = memories.first()
                "I remember ${relevantMemory.content}. ${generateBasicResponse(userInput)}"
            }
            else -> generateBasicResponse(userInput)
        }
    }

    private fun generateBasicResponse(input: String): String {
        // Basic response generation
        return when {
            input.lowercase().contains("hello") -> "Hello! It's great to hear from you."
            input.lowercase().contains("how are you") -> "I'm doing well, thank you for asking!"
            input.lowercase().contains("thank") -> "You're very welcome!"
            else -> "I understand. Is there anything specific I can help you with?"
        }
    }

    private fun getCurrentContext(): InteractionContext {
        val calendar = java.util.Calendar.getInstance()
        return InteractionContext(
            timeOfDay = calendar.get(java.util.Calendar.HOUR_OF_DAY),
            dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK)
        )
    }

    /**
     * Log interaction for learning service
     */
    private fun logInteractionForLearning(command: String, response: String, success: Boolean) {
        try {
            val intent = android.content.Intent(context, UserLearningService::class.java).apply {
                action = UserLearningService.ACTION_LOG_INTERACTION
                putExtra(UserLearningService.EXTRA_COMMAND, command)
                putExtra(UserLearningService.EXTRA_RESPONSE, response)
                putExtra(UserLearningService.EXTRA_SUCCESS, success)
                putExtra(UserLearningService.EXTRA_EXECUTION_TIME, 0L)
            }
            context.startService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error logging interaction for learning", e)
        }
    }

    fun cleanup() {
        serviceScope.cancel()
        mlPersonalizationService.cleanup()
        webSearchService.cleanup()
        personalMemoryService.cleanup()
    }
}

/**
 * Data classes for AI responses
 */
data class AIResponse(
    val text: String,
    val type: String,
    val confidence: Float,
    val suggestions: List<ProactiveSuggestion> = emptyList(),
    val hasSearchResults: Boolean = false,
    val searchResults: List<SearchResultItem> = emptyList()
)

data class BaseAIResponse(
    val text: String,
    val type: String,
    val confidence: Float,
    val hasSearchResults: Boolean = false,
    val searchResults: List<SearchResultItem> = emptyList()
)

data class PersonalizedResponse(
    val text: String,
    val confidence: Float
)
