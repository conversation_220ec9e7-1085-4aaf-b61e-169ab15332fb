package com.zara.assistant.utils

import android.content.Context
import android.util.Log
import java.io.IOException
import java.util.Properties

/**
 * Manager for securely loading API keys from assets
 */
class ApiKeyManager(private val context: Context) {

    companion object {
        private const val TAG = "ApiKeyManager"
        private const val API_KEYS_FILE = "api_keys.key"
    }
    
    private var apiKeys: Properties? = null
    
    /**
     * Initialize API keys from assets
     */
    fun initialize() {
        try {
            val inputStream = context.assets.open(API_KEYS_FILE)
            apiKeys = Properties().apply {
                load(inputStream)
            }
            inputStream.close()
            Log.d(TAG, "API keys loaded successfully")
        } catch (e: IOException) {
            Log.e(TAG, "Failed to load API keys from assets", e)
            // Fallback to BuildConfig if assets file not found
            initializeFallback()
        }
    }
    
    /**
     * Fallback to default values
     */
    private fun initializeFallback() {
        apiKeys = Properties().apply {
            setProperty("COHERE_API_KEY", "placeholder")
            setProperty("PERPLEXITY_API_KEY", "placeholder")
            setProperty("PORCUPINE_ACCESS_KEY", "placeholder")
            setProperty("AZURE_SPEECH_KEY", "placeholder")
            setProperty("AZURE_SPEECH_REGION", "eastus")
        }
        Log.d(TAG, "Using fallback API keys")
    }
    
    /**
     * Get Cohere API key
     */
    fun getCohereApiKey(): String {
        return getApiKey("COHERE_API_KEY") ?: ""
    }
    
    /**
     * Get Perplexity API key
     */
    fun getPerplexityApiKey(): String {
        return getApiKey("PERPLEXITY_API_KEY") ?: ""
    }
    
    /**
     * Get Porcupine access key
     */
    fun getPorcupineAccessKey(): String {
        return getApiKey("PORCUPINE_ACCESS_KEY") ?: ""
    }
    
    /**
     * Get Azure Speech API key
     */
    fun getAzureSpeechKey(): String {
        return getApiKey("AZURE_SPEECH_KEY") ?: ""
    }

    /**
     * Get Azure Speech region
     */
    fun getAzureSpeechRegion(): String {
        return getApiKey("AZURE_SPEECH_REGION") ?: ""
    }

    /**
     * Get OpenAI API key (optional)
     */
    fun getOpenAiApiKey(): String {
        return getApiKey("OPENAI_API_KEY") ?: ""
    }
    
    /**
     * Get any API key by name
     */
    private fun getApiKey(keyName: String): String? {
        val key = apiKeys?.getProperty(keyName)
        if (key.isNullOrEmpty() || key.contains("your_") || key.contains("_here")) {
            Log.w(TAG, "API key '$keyName' is not properly configured")
            return null
        }
        return key
    }
    
    /**
     * Check if all required API keys are configured
     */
    fun areApiKeysConfigured(): Boolean {
        val cohereKey = getCohereApiKey()
        val perplexityKey = getPerplexityApiKey()
        val porcupineKey = getPorcupineAccessKey()
        val azureSpeechKey = getAzureSpeechKey()
        val azureSpeechRegion = getAzureSpeechRegion()

        return cohereKey.isNotEmpty() && perplexityKey.isNotEmpty() &&
               porcupineKey.isNotEmpty() && azureSpeechKey.isNotEmpty() &&
               azureSpeechRegion.isNotEmpty()
    }
    
    /**
     * Get list of missing API keys
     */
    fun getMissingApiKeys(): List<String> {
        val missing = mutableListOf<String>()

        if (getCohereApiKey().isEmpty()) missing.add("Cohere API Key")
        if (getPerplexityApiKey().isEmpty()) missing.add("Perplexity API Key")
        if (getPorcupineAccessKey().isEmpty()) missing.add("Porcupine Access Key")
        if (getAzureSpeechKey().isEmpty()) missing.add("Azure Speech API Key")
        if (getAzureSpeechRegion().isEmpty()) missing.add("Azure Speech Region")

        return missing
    }
}
