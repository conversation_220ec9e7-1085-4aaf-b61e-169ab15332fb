package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.AIResponse
import com.zara.assistant.domain.model.AISource
import com.zara.assistant.domain.model.SystemAction
import com.zara.assistant.core.Constants
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for AI-related operations
 */
interface AIRepository {
    
    /**
     * AI response generation
     */
    suspend fun generateResponse(
        command: VoiceCommand,
        conversationHistory: List<String> = emptyList(),
        personality: Constants.AIPersonality = Constants.AIPersonality.FRIENDLY,
        responseStyle: Constants.AIResponseStyle = Constants.AIResponseStyle.CONVERSATIONAL
    ): Result<AIResponse>
    
    /**
     * Command processing
     */
    suspend fun processCommand(command: VoiceCommand): Result<AIResponse>
    suspend fun classifyCommand(text: String): Result<com.zara.assistant.domain.model.CommandType>
    suspend fun extractSystemActions(response: AIResponse): List<SystemAction>
    
    /**
     * Cohere API operations
     */
    suspend fun generateCohereResponse(
        prompt: String,
        maxTokens: Int = Constants.AI.MAX_TOKENS,
        temperature: Float = Constants.AI.TEMPERATURE
    ): Result<String>
    
    /**
     * Perplexity API operations
     */
    suspend fun searchPerplexity(
        query: String,
        maxTokens: Int = Constants.AI.MAX_TOKENS
    ): Result<String>
    
    /**
     * Context and conversation management
     */
    suspend fun buildContextPrompt(
        command: String,
        conversationHistory: List<String>,
        personality: Constants.AIPersonality,
        responseStyle: Constants.AIResponseStyle
    ): String
    
    suspend fun summarizeConversation(messages: List<String>): Result<String>
    
    /**
     * Response caching
     */
    suspend fun getCachedResponse(commandHash: String): AIResponse?
    suspend fun cacheResponse(commandHash: String, response: AIResponse)
    suspend fun clearResponseCache()
    
    /**
     * AI service health
     */
    suspend fun checkCohereHealth(): Result<Boolean>
    suspend fun checkPerplexityHealth(): Result<Boolean>
    fun getPreferredAISource(): Flow<AISource>
    suspend fun setPreferredAISource(source: AISource)
    
    /**
     * Response streaming
     */
    fun streamResponse(command: VoiceCommand): Flow<String>
    
    /**
     * Error handling and fallbacks
     */
    suspend fun getFallbackResponse(command: VoiceCommand): AIResponse
    suspend fun handleAPIError(error: Throwable, command: VoiceCommand): AIResponse
}
