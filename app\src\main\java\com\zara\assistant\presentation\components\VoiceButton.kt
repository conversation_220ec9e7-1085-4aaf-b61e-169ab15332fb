package com.zara.assistant.presentation.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.theme.DeepTeal
import com.zara.assistant.presentation.theme.LuminousBlue
import com.zara.assistant.presentation.theme.NeuroLightHighlight
import com.zara.assistant.presentation.theme.NeuroLightShadow
import com.zara.assistant.presentation.theme.NeuroSurfaceLight
import com.zara.assistant.presentation.theme.SoftCoral
import com.zara.assistant.presentation.theme.VoiceListening
import com.zara.assistant.presentation.theme.VoiceProcessing
import com.zara.assistant.presentation.theme.VoiceSpeaking

/**
 * Main voice interaction button with neumorphism design
 */
@Composable
fun VoiceButton(
    voiceState: Constants.VoiceState,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Dp = 120.dp,
    elevation: Dp = 12.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val animatedElevation by animateFloatAsState(
        targetValue = if (isPressed) elevation.value * 0.4f else elevation.value,
        animationSpec = tween(durationMillis = 150),
        label = "elevation"
    )
    
    // Infinite animation for listening/processing states
    val infiniteTransition = rememberInfiniteTransition(label = "voice_animation")
    val animatedAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )
    
    val rippleScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "ripple"
    )
    
    // Determine colors based on voice state
    val (iconColor, gradientColors, shouldAnimate) = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> {
            Triple(
                Color.White,
                listOf(VoiceListening, LuminousBlue),
                true
            )
        }
        Constants.VoiceState.PROCESSING -> {
            Triple(
                Color.White,
                listOf(VoiceProcessing, SoftCoral),
                true
            )
        }
        Constants.VoiceState.SPEAKING -> {
            Triple(
                Color.White,
                listOf(VoiceSpeaking, DeepTeal),
                true
            )
        }
        Constants.VoiceState.ERROR -> {
            Triple(
                Color.White,
                listOf(Color.Red, SoftCoral),
                false
            )
        }
        else -> {
            Triple(
                MaterialTheme.colorScheme.onSurface,
                listOf(DeepTeal, LuminousBlue),
                false
            )
        }
    }

    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        // Animated ripple effect for active states
        if (shouldAnimate) {
            Canvas(
                modifier = Modifier.size(size * rippleScale)
            ) {
                drawCircle(
                    color = gradientColors.first().copy(alpha = animatedAlpha * 0.3f),
                    radius = this.size.minDimension / 2
                )
            }
        }
        
        // Main button
        Box(
            modifier = Modifier
                .size(size)
                .clip(CircleShape)
                .drawBehind {
                    drawIntoCanvas { canvas ->
                        val paint = Paint()
                        val frameworkPaint = paint.asFrameworkPaint()
                        
                        if (!isPressed) {
                            // Draw shadow (bottom-right)
                            frameworkPaint.color = shadowColor.toArgb()
                            frameworkPaint.setShadowLayer(
                                animatedElevation,
                                animatedElevation,
                                animatedElevation,
                                shadowColor.toArgb()
                            )
                            canvas.drawCircle(
                                center = androidx.compose.ui.geometry.Offset(
                                    x = this.size.width / 2 + animatedElevation / 2,
                                    y = this.size.height / 2 + animatedElevation / 2
                                ),
                                radius = this.size.minDimension / 2 - animatedElevation,
                                paint = paint
                            )
                            
                            // Draw highlight (top-left)
                            frameworkPaint.color = highlightColor.toArgb()
                            frameworkPaint.setShadowLayer(
                                animatedElevation,
                                -animatedElevation,
                                -animatedElevation,
                                highlightColor.toArgb()
                            )
                            canvas.drawCircle(
                                center = androidx.compose.ui.geometry.Offset(
                                    x = this.size.width / 2 - animatedElevation / 2,
                                    y = this.size.height / 2 - animatedElevation / 2
                                ),
                                radius = this.size.minDimension / 2 - animatedElevation,
                                paint = paint
                            )
                        }
                    }
                }
                .background(
                    color = backgroundColor,
                    shape = CircleShape
                )
                .clickable(
                    interactionSource = interactionSource,
                    indication = null,
                    onClick = onClick
                ),
            contentAlignment = Alignment.Center
        ) {
            // Inner gradient circle for icon background
            Box(
                modifier = Modifier
                    .size(size * 0.6f)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.linearGradient(
                            colors = gradientColors.map { 
                                if (shouldAnimate) it.copy(alpha = animatedAlpha) else it 
                            }
                        ),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Mic,
                    contentDescription = "Voice Button",
                    tint = iconColor,
                    modifier = Modifier.size(size * 0.3f)
                )
            }
        }
    }
}
