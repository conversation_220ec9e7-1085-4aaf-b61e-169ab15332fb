package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Represents a system action that can be executed by the assistant
 */
@Parcelize
data class SystemAction(
    val type: ActionType,
    val target: String = "",
    val parameters: Map<String, String> = emptyMap(),
    val description: String = "",
    val requiresConfirmation: Boolean = false
) : Parcelable

/**
 * Types of system actions that can be performed
 */
enum class ActionType {
    // App Management
    OPEN_APP,
    CLOSE_APP,
    SWITCH_APP,
    KILL_APP,
    
    // Device Settings
    TOGGLE_WIFI,
    TOGGLE_BLUETOOTH,
    TOGGLE_AIRPLANE_MODE,
    TOGGLE_MOBILE_DATA,
    TOGGLE_HOTSPOT,
    TOGGLE_NFC,
    TOGGLE_LOCATION,
    TOGGLE_AUTO_ROTATE,
    TOGGLE_DO_NOT_DISTURB,
    
    // Audio & Display
    ADJUST_VOLUME,
    SET_VOLUME,
    MUTE_VOLUME,
    SET_BRIGHTNESS,
    ADJUST_BRIGHTNESS,
    TOG<PERSON><PERSON>_FLASHLIGHT,
    
    // System Navigation
    GO_HOME,
    GO_BACK,
    OPEN_RECENT_APPS,
    OPEN_NOTIFICATIONS,
    OPEN_QUICK_SETTINGS,
    
    // Device Actions
    TAKE_SCREENSHOT,
    LOCK_SCREEN,
    POWER_OFF,
    RESTART,
    
    // Communication
    MAKE_CALL,
    SEND_SMS,
    SEND_EMAIL,
    
    // Media Control
    PLAY_MUSIC,
    PAUSE_MUSIC,
    NEXT_TRACK,
    PREVIOUS_TRACK,
    STOP_MUSIC,
    
    // Location & Navigation
    OPEN_MAPS,
    GET_DIRECTIONS,
    SHARE_LOCATION,
    
    // Calendar & Time
    SET_ALARM,
    SET_TIMER,
    SET_REMINDER,
    CREATE_EVENT,
    
    // Settings Access
    OPEN_SETTINGS,
    OPEN_WIFI_SETTINGS,
    OPEN_BLUETOOTH_SETTINGS,
    OPEN_DISPLAY_SETTINGS,
    OPEN_SOUND_SETTINGS,
    OPEN_SECURITY_SETTINGS,
    OPEN_ACCESSIBILITY_SETTINGS,
    
    // File Operations
    OPEN_FILE,
    SHARE_FILE,
    DELETE_FILE,
    
    // Web & Search
    OPEN_BROWSER,
    SEARCH_WEB,
    OPEN_URL,
    
    // Unknown/Unsupported
    UNKNOWN
}

/**
 * Extension functions for ActionType
 */
fun ActionType.isSystemControl(): Boolean {
    return when (this) {
        ActionType.TOGGLE_WIFI,
        ActionType.TOGGLE_BLUETOOTH,
        ActionType.TOGGLE_AIRPLANE_MODE,
        ActionType.TOGGLE_MOBILE_DATA,
        ActionType.TOGGLE_HOTSPOT,
        ActionType.TOGGLE_NFC,
        ActionType.TOGGLE_LOCATION,
        ActionType.TOGGLE_AUTO_ROTATE,
        ActionType.TOGGLE_DO_NOT_DISTURB,
        ActionType.ADJUST_VOLUME,
        ActionType.SET_VOLUME,
        ActionType.MUTE_VOLUME,
        ActionType.SET_BRIGHTNESS,
        ActionType.ADJUST_BRIGHTNESS,
        ActionType.TOGGLE_FLASHLIGHT -> true
        else -> false
    }
}

fun ActionType.isAppControl(): Boolean {
    return when (this) {
        ActionType.OPEN_APP,
        ActionType.CLOSE_APP,
        ActionType.SWITCH_APP,
        ActionType.KILL_APP -> true
        else -> false
    }
}

fun ActionType.isNavigation(): Boolean {
    return when (this) {
        ActionType.GO_HOME,
        ActionType.GO_BACK,
        ActionType.OPEN_RECENT_APPS,
        ActionType.OPEN_NOTIFICATIONS,
        ActionType.OPEN_QUICK_SETTINGS -> true
        else -> false
    }
}

fun ActionType.isMediaControl(): Boolean {
    return when (this) {
        ActionType.PLAY_MUSIC,
        ActionType.PAUSE_MUSIC,
        ActionType.NEXT_TRACK,
        ActionType.PREVIOUS_TRACK,
        ActionType.STOP_MUSIC -> true
        else -> false
    }
}

fun ActionType.requiresPermission(): Boolean {
    return when (this) {
        ActionType.MAKE_CALL,
        ActionType.SEND_SMS,
        ActionType.SEND_EMAIL,
        ActionType.GET_DIRECTIONS,
        ActionType.SHARE_LOCATION,
        ActionType.TAKE_SCREENSHOT -> true
        else -> false
    }
}

fun ActionType.isDestructive(): Boolean {
    return when (this) {
        ActionType.KILL_APP,
        ActionType.DELETE_FILE,
        ActionType.POWER_OFF,
        ActionType.RESTART,
        ActionType.LOCK_SCREEN -> true
        else -> false
    }
}
