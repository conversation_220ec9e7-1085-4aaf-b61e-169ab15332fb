package com.zara.assistant.presentation.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zara.assistant.presentation.theme.NeuroLightHighlight
import com.zara.assistant.presentation.theme.NeuroLightShadow
import com.zara.assistant.presentation.theme.NeuroSurfaceLight

/**
 * Neumorphism style button component
 */
@Composable
fun NeumorphismButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    cornerRadius: Dp = 16.dp,
    elevation: Dp = 8.dp,
    contentPadding: PaddingValues = PaddingValues(horizontal = 24.dp, vertical = 16.dp),
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    textColor: Color = MaterialTheme.colorScheme.onSurface
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val animatedElevation by animateFloatAsState(
        targetValue = if (isPressed) elevation.value * 0.3f else elevation.value,
        animationSpec = tween(durationMillis = 150),
        label = "elevation"
    )
    
    val animatedOffset by animateFloatAsState(
        targetValue = if (isPressed) 2f else 0f,
        animationSpec = tween(durationMillis = 150),
        label = "offset"
    )

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(cornerRadius))
            .drawBehind {
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    
                    if (!isPressed) {
                        // Draw shadow (bottom-right)
                        frameworkPaint.color = shadowColor.toArgb()
                        frameworkPaint.setShadowLayer(
                            animatedElevation,
                            animatedElevation,
                            animatedElevation,
                            shadowColor.toArgb()
                        )
                        canvas.drawRoundRect(
                            left = animatedElevation,
                            top = animatedElevation,
                            right = size.width,
                            bottom = size.height,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                        
                        // Draw highlight (top-left)
                        frameworkPaint.color = highlightColor.toArgb()
                        frameworkPaint.setShadowLayer(
                            animatedElevation,
                            -animatedElevation,
                            -animatedElevation,
                            highlightColor.toArgb()
                        )
                        canvas.drawRoundRect(
                            left = 0f,
                            top = 0f,
                            right = size.width - animatedElevation,
                            bottom = size.height - animatedElevation,
                            radiusX = cornerRadius.toPx(),
                            radiusY = cornerRadius.toPx(),
                            paint = paint
                        )
                    }
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled,
                onClick = onClick
            )
            .padding(contentPadding),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (enabled) textColor else textColor.copy(alpha = 0.6f),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
