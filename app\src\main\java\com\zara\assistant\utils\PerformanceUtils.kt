package com.zara.assistant.utils

import android.content.Context
import android.os.Build
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.RandomAccessFile
import kotlin.system.measureTimeMillis

/**
 * Utility class for performance monitoring and optimization
 */
object PerformanceUtils {
    
    const val TAG = "PerformanceUtils"
    
    /**
     * Measure execution time of a suspend function
     */
    suspend inline fun <T> measureSuspendTime(
        operation: String,
        block: suspend () -> T
    ): T {
        val result: T
        val time = measureTimeMillis {
            result = block()
        }
        Log.d(TAG, "$operation took ${time}ms")
        return result
    }
    
    /**
     * Measure execution time of a regular function
     */
    inline fun <T> measureTime(
        operation: String,
        block: () -> T
    ): T {
        val result: T
        val time = measureTimeMillis {
            result = block()
        }
        Log.d(TAG, "$operation took ${time}ms")
        return result
    }
    
    /**
     * Get current memory usage
     */
    fun getMemoryUsage(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        
        return MemoryInfo(
            maxMemory = maxMemory,
            totalMemory = totalMemory,
            usedMemory = usedMemory,
            freeMemory = freeMemory,
            availableMemory = maxMemory - usedMemory
        )
    }
    
    /**
     * Log current memory usage
     */
    fun logMemoryUsage(tag: String = TAG) {
        val memInfo = getMemoryUsage()
        Log.d(tag, "Memory Usage: ${memInfo.usedMemory / 1024 / 1024}MB used, " +
                "${memInfo.availableMemory / 1024 / 1024}MB available")
    }
    
    /**
     * Get CPU usage (requires API level 26+)
     */
    fun getCpuUsage(): Float {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val file = RandomAccessFile("/proc/stat", "r")
                val cpuLine = file.readLine()
                file.close()
                
                val times = cpuLine.split(" ").drop(2).take(4).map { it.toLong() }
                val idleTime = times[3]
                val totalTime = times.sum()
                
                ((totalTime - idleTime).toFloat() / totalTime.toFloat()) * 100f
            } else {
                -1f // Not supported
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting CPU usage", e)
            -1f
        }
    }
    
    /**
     * Check if device is low on memory
     */
    fun isLowMemory(): Boolean {
        val memInfo = getMemoryUsage()
        val usagePercentage = (memInfo.usedMemory.toFloat() / memInfo.maxMemory.toFloat()) * 100f
        return usagePercentage > 85f
    }
    
    /**
     * Force garbage collection (use sparingly)
     */
    fun forceGarbageCollection() {
        Log.d(TAG, "Forcing garbage collection")
        System.gc()
        System.runFinalization()
    }
    
    /**
     * Clear app cache
     */
    fun clearCache(context: Context) {
        try {
            val cacheDir = context.cacheDir
            deleteDir(cacheDir)
            Log.d(TAG, "Cache cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing cache", e)
        }
    }
    
    /**
     * Get cache size
     */
    fun getCacheSize(context: Context): Long {
        return try {
            getDirSize(context.cacheDir)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cache size", e)
            0L
        }
    }
    
    /**
     * Optimize for battery usage
     */
    fun optimizeForBattery(context: Context) {
        // Reduce background processing
        // Lower wake word sensitivity
        // Reduce AI response frequency
        Log.d(TAG, "Optimizing for battery usage")
    }
    
    /**
     * Optimize for performance
     */
    fun optimizeForPerformance(context: Context) {
        // Increase buffer sizes
        // Pre-load models
        // Increase wake word sensitivity
        Log.d(TAG, "Optimizing for performance")
    }
    
    /**
     * Monitor performance in background
     */
    fun startPerformanceMonitoring(scope: CoroutineScope) {
        scope.launch {
            while (true) {
                withContext(Dispatchers.IO) {
                    logMemoryUsage()
                    
                    if (isLowMemory()) {
                        Log.w(TAG, "Low memory detected!")
                        forceGarbageCollection()
                    }
                    
                    kotlinx.coroutines.delay(30000) // Check every 30 seconds
                }
            }
        }
    }
    
    private fun deleteDir(dir: File?): Boolean {
        return if (dir != null && dir.isDirectory) {
            val children = dir.list()
            if (children != null) {
                for (child in children) {
                    val success = deleteDir(File(dir, child))
                    if (!success) {
                        return false
                    }
                }
            }
            dir.delete()
        } else if (dir != null && dir.isFile) {
            dir.delete()
        } else {
            false
        }
    }
    
    private fun getDirSize(dir: File): Long {
        var size = 0L
        if (dir.isDirectory) {
            val files = dir.listFiles()
            if (files != null) {
                for (file in files) {
                    size += if (file.isDirectory) {
                        getDirSize(file)
                    } else {
                        file.length()
                    }
                }
            }
        } else {
            size = dir.length()
        }
        return size
    }
}

/**
 * Data class for memory information
 */
data class MemoryInfo(
    val maxMemory: Long,
    val totalMemory: Long,
    val usedMemory: Long,
    val freeMemory: Long,
    val availableMemory: Long
) {
    fun getUsagePercentage(): Float {
        return (usedMemory.toFloat() / maxMemory.toFloat()) * 100f
    }
    
    fun getUsedMemoryMB(): Long {
        return usedMemory / 1024 / 1024
    }
    
    fun getAvailableMemoryMB(): Long {
        return availableMemory / 1024 / 1024
    }
}
