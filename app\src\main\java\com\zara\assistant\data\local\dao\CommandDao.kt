package com.zara.assistant.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.zara.assistant.data.local.database.entities.VoiceCommandEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for voice command operations
 */
@Dao
interface CommandDao {
    
    @Query("SELECT * FROM voice_commands ORDER BY timestamp DESC")
    fun getAllCommands(): Flow<List<VoiceCommandEntity>>
    
    @Query("SELECT * FROM voice_commands WHERE id = :id")
    suspend fun getCommandById(id: String): VoiceCommandEntity?
    
    @Query("SELECT * FROM voice_commands ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentCommands(limit: Int): List<VoiceCommandEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCommand(command: VoiceCommandEntity)
    
    @Query("DELETE FROM voice_commands WHERE id = :id")
    suspend fun deleteCommandById(id: String)
    
    @Query("DELETE FROM voice_commands")
    suspend fun deleteAllCommands()
    
    @Query("SELECT * FROM voice_commands WHERE text LIKE '%' || :query || '%'")
    suspend fun searchCommands(query: String): List<VoiceCommandEntity>
    
    @Query("SELECT COUNT(*) FROM voice_commands WHERE isProcessed = 1")
    suspend fun getProcessedCommandCount(): Int
    
    @Query("SELECT COUNT(*) FROM voice_commands WHERE isProcessed = 0")
    suspend fun getFailedCommandCount(): Int
}
