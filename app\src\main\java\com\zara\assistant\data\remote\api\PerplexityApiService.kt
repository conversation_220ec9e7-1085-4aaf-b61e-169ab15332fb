package com.zara.assistant.data.remote.api

import com.zara.assistant.data.remote.dto.PerplexityChatRequest
import com.zara.assistant.data.remote.dto.PerplexityResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Perplexity API service interface
 */
interface PerplexityApiService {
    
    @POST("chat/completions")
    suspend fun chatCompletion(
        @Body request: PerplexityChatRequest
    ): Response<PerplexityResponse>
}

/**
 * Perplexity API request models
 */
data class PerplexityRequest(
    val model: String = "llama-3.1-sonar-small-128k-online",
    val messages: List<PerplexityMessage>,
    val max_tokens: Int = 150,
    val temperature: Float = 0.7f,
    val top_p: Float = 0.9f,
    val return_citations: Boolean = true,
    val search_domain_filter: List<String> = emptyList(),
    val return_images: Boolean = false,
    val return_related_questions: <PERSON><PERSON>an = false,
    val search_recency_filter: String = "month",
    val top_k: Int = 0,
    val stream: Boolean = false,
    val presence_penalty: Float = 0f,
    val frequency_penalty: Float = 1f
)

data class PerplexityMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)
