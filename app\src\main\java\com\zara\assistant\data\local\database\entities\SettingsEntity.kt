package com.zara.assistant.data.local.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Room entity for app settings
 */
@Entity(tableName = "settings")
data class SettingsEntity(
    @PrimaryKey
    val key: String,
    val value: String,
    val type: String // STRING, BOOLEAN, FLOAT, INT
)

/**
 * Room entity for voice commands
 */
@Entity(tableName = "voice_commands")
data class VoiceCommandEntity(
    @PrimaryKey
    val id: String,
    val text: String,
    val timestamp: Long,
    val confidence: Float,
    val language: String,
    val isProcessed: Boolean = false,
    val processingTime: Long = 0L,
    val commandType: String = "UNKNOWN"
)
