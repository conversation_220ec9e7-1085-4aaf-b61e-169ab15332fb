package com.zara.assistant.data.repository

import com.google.gson.Gson
import com.zara.assistant.data.local.dao.ConversationDao
import com.zara.assistant.data.local.database.entities.ConversationEntity
import com.zara.assistant.data.local.database.entities.ConversationMessageEntity
import com.zara.assistant.domain.model.AIResponse
import com.zara.assistant.domain.model.Conversation
import com.zara.assistant.domain.model.ConversationMessage
import com.zara.assistant.domain.model.MessageSender
import com.zara.assistant.domain.model.MessageType
import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.repository.ConversationRepository
import com.zara.assistant.domain.repository.ConversationStats
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of ConversationRepository
 */
@Singleton
class ConversationRepositoryImpl @Inject constructor(
    private val conversationDao: ConversationDao,
    private val gson: Gson = Gson()
) : ConversationRepository {

    override suspend fun createConversation(): Result<Conversation> {
        return try {
            val conversation = Conversation(
                id = UUID.randomUUID().toString(),
                startTime = Date(),
                isActive = true
            )
            
            val entity = conversation.toEntity()
            conversationDao.insertConversation(entity)
            
            Result.success(conversation)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getActiveConversation(): Conversation? {
        return try {
            conversationDao.getActiveConversation()?.toDomain()
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getConversation(id: String): Conversation? {
        return try {
            conversationDao.getConversationById(id)?.toDomain()
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getAllConversations(): Flow<List<Conversation>> {
        return conversationDao.getAllConversations().map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun endConversation(id: String): Result<Unit> {
        return try {
            val conversation = conversationDao.getConversationById(id)
            if (conversation != null) {
                val updatedConversation = conversation.copy(
                    endTime = Date(),
                    isActive = false
                )
                conversationDao.updateConversation(updatedConversation)
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteConversation(id: String): Result<Unit> {
        return try {
            conversationDao.deleteConversationById(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun clearAllConversations(): Result<Unit> {
        return try {
            conversationDao.deleteAllConversations()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addMessage(conversationId: String, message: ConversationMessage): Result<Unit> {
        return try {
            val entity = message.toEntity()
            conversationDao.insertMessage(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addUserCommand(conversationId: String, command: VoiceCommand): Result<ConversationMessage> {
        return try {
            val message = ConversationMessage(
                id = UUID.randomUUID().toString(),
                conversationId = conversationId,
                content = command.text,
                timestamp = command.timestamp,
                sender = MessageSender.USER,
                messageType = MessageType.VOICE,
                metadata = mapOf(
                    "confidence" to command.confidence.toString(),
                    "language" to command.language,
                    "commandType" to command.commandType.name
                )
            )
            
            addMessage(conversationId, message)
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun addAIResponse(conversationId: String, response: AIResponse): Result<ConversationMessage> {
        return try {
            val message = ConversationMessage(
                id = UUID.randomUUID().toString(),
                conversationId = conversationId,
                content = response.text,
                timestamp = response.timestamp,
                sender = MessageSender.ZARA,
                messageType = MessageType.TEXT,
                metadata = mapOf(
                    "responseTime" to response.responseTime.toString(),
                    "source" to response.source.name,
                    "confidence" to response.confidence.toString(),
                    "actions" to gson.toJson(response.actions)
                )
            )
            
            addMessage(conversationId, message)
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getMessages(conversationId: String): Flow<List<ConversationMessage>> {
        return conversationDao.getMessagesForConversation(conversationId).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun getRecentMessages(conversationId: String, limit: Int): List<ConversationMessage> {
        return try {
            conversationDao.getRecentMessages(conversationId, limit).map { it.toDomain() }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun deleteMessage(messageId: String): Result<Unit> {
        return try {
            conversationDao.deleteMessageById(messageId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getConversationHistory(conversationId: String): List<String> {
        return try {
            val messages = conversationDao.getRecentMessages(conversationId, 10)
            messages.map { "${it.sender}: ${it.content}" }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun getConversationSummary(conversationId: String): String? {
        return try {
            conversationDao.getConversationById(conversationId)?.summary
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun updateConversationSummary(conversationId: String, summary: String): Result<Unit> {
        return try {
            val conversation = conversationDao.getConversationById(conversationId)
            if (conversation != null) {
                val updatedConversation = conversation.copy(summary = summary)
                conversationDao.updateConversation(updatedConversation)
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun searchConversations(query: String): List<Conversation> {
        return try {
            // Simple implementation - in a real app, you'd have a proper search
            val allConversations = conversationDao.getAllConversations()
            // This is a simplified search - you'd implement proper full-text search
            emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun searchMessages(query: String): List<ConversationMessage> {
        return try {
            conversationDao.searchMessages(query).map { it.toDomain() }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun getConversationsByDateRange(startDate: Long, endDate: Long): List<Conversation> {
        return try {
            conversationDao.getConversationsByDateRange(startDate, endDate).map { it.toDomain() }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun getConversationStats(): ConversationStats {
        return try {
            // Implementation would calculate actual stats from database
            ConversationStats(
                totalConversations = 0,
                totalMessages = 0,
                averageConversationLength = 0,
                averageResponseTime = 0L,
                mostActiveDay = "",
                totalVoiceCommands = 0,
                successfulCommands = 0,
                failedCommands = 0
            )
        } catch (e: Exception) {
            ConversationStats(0, 0, 0, 0L, "", 0, 0, 0)
        }
    }

    override suspend fun getMessageCount(conversationId: String): Int {
        return try {
            conversationDao.getMessageCount(conversationId)
        } catch (e: Exception) {
            0
        }
    }

    override suspend fun getAverageResponseTime(conversationId: String): Long {
        return try {
            // Implementation would calculate from message timestamps
            0L
        } catch (e: Exception) {
            0L
        }
    }

    override suspend fun exportConversation(conversationId: String): Result<String> {
        return try {
            val conversation = getConversation(conversationId)
            val messages = getRecentMessages(conversationId, Int.MAX_VALUE)
            val exportData = mapOf(
                "conversation" to conversation,
                "messages" to messages
            )
            Result.success(gson.toJson(exportData))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun exportAllConversations(): Result<String> {
        return try {
            // Implementation would export all conversations
            Result.success("{}")
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun importConversations(data: String): Result<Unit> {
        return try {
            // Implementation would parse and import conversation data
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // Extension functions for entity conversion
    private fun Conversation.toEntity(): ConversationEntity {
        return ConversationEntity(
            id = id,
            startTime = startTime,
            endTime = endTime,
            isActive = isActive,
            summary = summary,
            totalMessages = totalMessages,
            averageResponseTime = averageResponseTime
        )
    }

    private fun ConversationEntity.toDomain(): Conversation {
        return Conversation(
            id = id,
            startTime = startTime,
            endTime = endTime,
            isActive = isActive,
            summary = summary,
            totalMessages = totalMessages,
            averageResponseTime = averageResponseTime
        )
    }

    private fun ConversationMessage.toEntity(): ConversationMessageEntity {
        return ConversationMessageEntity(
            id = id,
            conversationId = conversationId,
            content = content,
            timestamp = timestamp,
            sender = sender.name,
            messageType = messageType.name,
            metadata = gson.toJson(metadata)
        )
    }

    private fun ConversationMessageEntity.toDomain(): ConversationMessage {
        return ConversationMessage(
            id = id,
            conversationId = conversationId,
            content = content,
            timestamp = timestamp,
            sender = MessageSender.valueOf(sender),
            messageType = MessageType.valueOf(messageType),
            metadata = try {
                gson.fromJson(metadata, Map::class.java) as Map<String, String>
            } catch (e: Exception) {
                emptyMap()
            }
        )
    }
}
