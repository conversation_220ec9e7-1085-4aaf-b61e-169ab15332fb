package com.zara.assistant.services

import android.app.Notification
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.zara.assistant.R
import com.zara.assistant.ZaraApplication
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.domain.model.CommandType
import com.zara.assistant.domain.model.ConversationState
import com.zara.assistant.domain.model.ConversationType
import com.zara.assistant.domain.model.SystemAction
import com.zara.assistant.domain.model.ActionType
import com.zara.assistant.domain.usecase.ProcessVoiceCommandUseCase
import com.zara.assistant.ui.MainActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Date
import java.util.Locale
import java.util.UUID
import javax.inject.Inject

/**
 * Advanced Voice Processing Service with intelligent local command processing
 * 
 * Features:
 * - Azure Speech Services for STT
 * - Local intelligence for system commands (no API calls)
 * - Smart routing: local vs AI API
 * - Perfect system control integration
 * - Real-time voice processing
 */
@AndroidEntryPoint
class AdvancedVoiceProcessingService : Service(), TextToSpeech.OnInitListener {

    companion object {
        private const val TAG = "AdvancedVoiceProcessing"
        private const val NOTIFICATION_ID = ZaraApplication.VOICE_PROCESSING_NOTIFICATION_ID
        
        const val ACTION_START_LISTENING = "START_LISTENING"
        const val ACTION_STOP_LISTENING = "STOP_LISTENING"
        const val ACTION_SPEAK_TEXT = "SPEAK_TEXT"
        const val EXTRA_TEXT_TO_SPEAK = "text_to_speak"
        
        fun startListening(context: Context) {
            Log.d(TAG, "🎤 startListening() companion method called")
            val intent = Intent(context, AdvancedVoiceProcessingService::class.java).apply {
                action = ACTION_START_LISTENING
            }
            Log.d(TAG, "🚀 Starting foreground service with intent: $intent")
            ContextCompat.startForegroundService(context, intent)
            Log.d(TAG, "✅ Foreground service start requested")
        }
        
        fun stopListening(context: Context) {
            val intent = Intent(context, AdvancedVoiceProcessingService::class.java).apply {
                action = ACTION_STOP_LISTENING
            }
            context.startService(intent)
        }
        
        fun speakText(context: Context, text: String) {
            val intent = Intent(context, AdvancedVoiceProcessingService::class.java).apply {
                action = ACTION_SPEAK_TEXT
                putExtra(EXTRA_TEXT_TO_SPEAK, text)
            }
            ContextCompat.startForegroundService(context, intent)
        }
    }

    private val binder = VoiceProcessingBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Voice Recognition - Azure-Only STT Service
    @Inject
    lateinit var azureOnlySTTService: AzureOnlySTTService
    private var textToSpeech: TextToSpeech? = null
    private var isListening = false
    private var isSpeaking = false
    private var isTtsInitialized = false
    private var listeningTimeoutJob: Job? = null

    // Local Intelligence
    @Inject
    lateinit var localCommandProcessor: LocalCommandProcessor

    @Inject
    lateinit var processVoiceCommandUseCase: ProcessVoiceCommandUseCase

    @Inject
    lateinit var systemControlManager: SystemControlManager

    @Inject
    lateinit var conversationManager: ConversationManager

    @Inject
    lateinit var aiOrchestrationService: AIOrchestrationService
    
    private val _voiceState = MutableStateFlow(
        VoiceState(
            currentState = VoiceState.State.IDLE,
            isWakeWordActive = false,
            isListening = false,
            isProcessing = false,
            isSpeaking = false
        )
    )
    val voiceState: StateFlow<VoiceState> = _voiceState.asStateFlow()

    inner class VoiceProcessingBinder : Binder() {
        fun getService(): AdvancedVoiceProcessingService = this@AdvancedVoiceProcessingService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🎯 AdvancedVoiceProcessingService onCreate() called")
        initializeTextToSpeech()
        initializeVoiceManager()
        Log.d(TAG, "✅ Advanced Voice Processing Service created successfully")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "🎯 onStartCommand() called with action: ${intent?.action}")
        when (intent?.action) {
            ACTION_START_LISTENING -> {
                Log.d(TAG, "📢 Received ACTION_START_LISTENING")
                startListening()
            }
            ACTION_STOP_LISTENING -> {
                Log.d(TAG, "📢 Received ACTION_STOP_LISTENING")
                stopListening()
            }
            ACTION_SPEAK_TEXT -> {
                Log.d(TAG, "📢 Received ACTION_SPEAK_TEXT")
                val text = intent.getStringExtra(EXTRA_TEXT_TO_SPEAK)
                if (!text.isNullOrEmpty()) {
                    speakText(text)
                }
            }
            else -> {
                Log.w(TAG, "⚠️ Unknown action received: ${intent?.action}")
            }
        }
        return START_NOT_STICKY
    }

    private fun startListening() {
        Log.d(TAG, "🎤 startListening() called - current state: isListening=$isListening")

        // Always reset state and start fresh listening session
        if (isListening) {
            Log.d(TAG, "Stopping current listening session to start new one")
            azureOnlySTTService.stopListening()
            isListening = false
        }

        try {
            startForeground(NOTIFICATION_ID, createNotification("Listening for your command..."))

            Log.d(TAG, "Starting new listening session with Azure Speech STT")

            // Initialize AI services if not already done
            serviceScope.launch {
                aiOrchestrationService.initialize()
            }

            // Check if Azure STT is initialized before starting
            if (azureOnlySTTService.isInitialized()) {
                azureOnlySTTService.startListening(createSTTListener(), false)
            } else {
                Log.w(TAG, "Azure STT not ready yet, initializing...")
                serviceScope.launch(Dispatchers.IO) {
                    val success = azureOnlySTTService.initialize()
                    withContext(Dispatchers.Main) {
                        if (success) {
                            Log.d(TAG, "Azure STT initialized, starting listening...")
                            azureOnlySTTService.startListening(createSTTListener(), false)
                        } else {
                            Log.e(TAG, "Failed to initialize Azure STT for listening")
                            updateVoiceState(VoiceState.State.ERROR, errorMessage = "Speech recognition not available")
                        }
                    }
                }
            }

            // Set timeout for listening session (15 seconds for voice commands)
            startListeningTimeout()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start listening", e)
            updateVoiceState(VoiceState.State.ERROR, errorMessage = e.message)
            stopSelf()
        }
    }

    private fun stopListening() {
        try {
            cancelListeningTimeout()
            azureOnlySTTService.stopListening()
            isListening = false

            // Check if we're in a conversational mode that should continue
            val isInConversation = conversationManager.isInConversation()
            val currentState = conversationManager.getCurrentState()

            Log.d(TAG, "📊 Stop listening - In conversation: $isInConversation, State: $currentState")

            if (isInConversation && (currentState == ConversationState.WAITING_FOR_CLARIFICATION ||
                                   currentState == ConversationState.PROCESSING ||
                                   currentState == ConversationState.EXECUTING)) {
                // Don't stop service during ANY active conversation - let TTS callback handle it
                Log.d(TAG, "🔄 Keeping service alive for active conversation (${currentState})")
                updateVoiceState(VoiceState.State.SPEAKING_RESPONSE, isListening = false)
            } else {
                // Normal stop behavior
                updateVoiceState(VoiceState.State.IDLE, isListening = false)
                Log.d(TAG, "Stopped listening")
                stopForeground(STOP_FOREGROUND_REMOVE)
                stopSelf()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping listening", e)
        }
    }

    private fun startListeningTimeout() {
        cancelListeningTimeout()
        listeningTimeoutJob = serviceScope.launch {
            delay(15000) // 15 seconds timeout for voice commands
            Log.d(TAG, "⏰ Listening timeout reached (15s), stopping listening")
            if (isListening) {
                updateVoiceState(VoiceState.State.IDLE, isListening = false)
                stopListening()
            } else {
                // Check if we're in extended chat mode and should keep service alive
                val isInConversation = conversationManager.isInConversation()
                val currentState = conversationManager.getCurrentState()

                if (isInConversation && (currentState == ConversationState.WAITING_FOR_CLARIFICATION ||
                                       currentState == ConversationState.PROCESSING ||
                                       currentState == ConversationState.EXECUTING)) {
                    Log.d(TAG, "⏰ Timeout reached but keeping service alive for active conversation (${currentState})")
                    // Start extended timeout for ANY active conversation
                    startExtendedChatTimeout()
                } else {
                    Log.d(TAG, "⏰ Timeout reached, stopping service")
                    updateVoiceState(VoiceState.State.IDLE, isListening = false)
                    stopSelf()
                }
            }
        }
    }

    private fun startExtendedChatTimeout() {
        Log.d(TAG, "⏰ Starting extended chat timeout (2 minutes)")
        cancelListeningTimeout()
        listeningTimeoutJob = serviceScope.launch {
            Log.d(TAG, "⏰ Extended chat timeout job started")
            delay(120000) // 2 minutes timeout for extended chat
            Log.d(TAG, "⏰ Extended chat timeout reached (2min), ending conversation")
            conversationManager.endConversation()
            updateVoiceState(VoiceState.State.IDLE, isListening = false)
            stopSelf()
        }
        Log.d(TAG, "⏰ Extended chat timeout job created successfully")
    }

    private fun cancelListeningTimeout() {
        if (listeningTimeoutJob != null) {
            Log.d(TAG, "⏰ Cancelling existing timeout job")
            listeningTimeoutJob?.cancel()
            listeningTimeoutJob = null
        } else {
            Log.d(TAG, "⏰ No timeout job to cancel")
        }
    }

    /**
     * Process recognized text with conversational intelligence
     */
    private fun processRecognizedText(text: String, confidence: Float) {
        serviceScope.launch {
            try {
                updateVoiceState(VoiceState.State.PROCESSING_COMMAND, isProcessing = true)

                Log.d(TAG, "🗣️ Processing conversational input: '$text'")

                // Process with conversation manager
                Log.d(TAG, "📊 Before processing - Conversation state: ${conversationManager.getCurrentState()}")
                Log.d(TAG, "📊 Before processing - Is in conversation: ${conversationManager.isInConversation()}")

                val commandResult = conversationManager.processUserInput(text)

                Log.d(TAG, "📊 After processing - Command result: complete=${commandResult.isComplete}, needsClarification=${commandResult.needsClarification}")
                Log.d(TAG, "📊 After processing - Conversation type: ${commandResult.parameters.intent}")

                if (commandResult.needsClarification) {
                    // Universal clarification for ALL conversation types
                    Log.d(TAG, "❓ Asking clarification: ${commandResult.clarificationQuestion}")
                    commandResult.clarificationQuestion?.let { question ->
                        conversationManager.addConversationTurn(text, question)
                        // Use conversational utterance ID for ALL clarification questions
                        speakTextAndContinueListening(question)
                        // Cancel existing timeouts and set extended timeout for ANY conversation
                        cancelListeningTimeout()
                        startExtendedChatTimeout()
                        Log.d(TAG, "⏰ Extended timeout set for conversation clarification")
                    }
                } else if (commandResult.isComplete && commandResult.canExecute) {
                    // Execute the complete command
                    Log.d(TAG, "✅ Executing complete command")
                    executeCompleteCommand(commandResult, text)
                } else {
                    // Fallback to original processing for non-conversational commands
                    Log.d(TAG, "🔄 Falling back to original processing")
                    processWithOriginalLogic(text, confidence)
                }

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error in conversational processing", e)
                updateVoiceState(VoiceState.State.ERROR, errorMessage = e.message)
                speakText("I'm sorry, I encountered an error processing your request.")
            }
        }
    }

    /**
     * Execute a complete command from conversation
     */
    private suspend fun executeCompleteCommand(commandResult: com.zara.assistant.domain.model.CommandResult, originalText: String) {
        val params = commandResult.parameters

        when (params.intent) {
            ConversationType.INFORMATION_REQUEST -> {
                val topic = params.getParameter("topic") ?: ""
                Log.d(TAG, "📚 Information request about: $topic")

                // Check if it's a built-in response or needs AI
                if (isBuiltInTopic(topic)) {
                    val response = generateInformationResponse(topic)
                    conversationManager.addConversationTurn(originalText, response)
                    speakText(response)
                    conversationManager.endConversation()
                } else {
                    // Use AI Orchestration Service for information requests
                    Log.d(TAG, "🤖 Using AI Orchestration for information about: $topic")
                    handleInformationRequestWithAI(originalText, topic)
                }
            }

            ConversationType.MUSIC_CONTROL -> {
                val action = params.getParameter("action") ?: ""
                val songName = params.getParameter("song_name") ?: ""
                val artistName = params.getParameter("artist_name") ?: ""
                Log.d(TAG, "🎵 Music control: $action - Song: '$songName' - Artist: '$artistName'")
                Log.d(TAG, "🎵 Original text: '$originalText'")

                // Execute actual music control using LocalCommandProcessor
                try {
                    val voiceCommand = VoiceCommand(
                        id = UUID.randomUUID().toString(),
                        text = originalText,
                        timestamp = Date(),
                        confidence = 1.0f,
                        language = Locale.getDefault().language
                    )

                    val localResult = localCommandProcessor.processCommand(voiceCommand)

                    when (localResult) {
                        is LocalCommandResult.SystemControl -> {
                            Log.d(TAG, "🎵 Executing music control: ${localResult.action.type}")
                            handleSystemControlLocally(localResult)
                            conversationManager.addConversationTurn(originalText, localResult.response)
                            speakText(localResult.response)
                        }
                        else -> {
                            // Fallback: Try to execute music control directly
                            Log.d(TAG, "🎵 LocalCommandProcessor didn't handle music, trying direct execution")

                            // Create SystemAction directly and execute
                            val musicParams = mutableMapOf<String, String>()
                            if (songName.isNotEmpty()) musicParams["song"] = songName
                            if (artistName.isNotEmpty()) musicParams["artist"] = artistName

                            val systemAction = SystemAction(
                                type = ActionType.PLAY_MUSIC,
                                parameters = musicParams
                            )

                            val executed = systemControlManager.executeSystemAction(systemAction)
                            val response = if (executed) {
                                if (songName.isNotEmpty()) {
                                    if (artistName.isNotEmpty()) {
                                        "Playing $songName by $artistName on Spotify"
                                    } else {
                                        "Playing $songName on Spotify"
                                    }
                                } else {
                                    "Starting music playback on Spotify"
                                }
                            } else {
                                "Sorry, I couldn't play the music right now"
                            }

                            conversationManager.addConversationTurn(originalText, response)
                            speakText(response)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ Error executing music control", e)
                    val errorResponse = "Sorry, I couldn't control the music right now."
                    conversationManager.addConversationTurn(originalText, errorResponse)
                    speakText(errorResponse)
                }

                conversationManager.endConversation()
            }

            ConversationType.MESSAGING -> {
                val recipient = params.getParameter("recipient") ?: ""
                val message = params.getParameter("message_content") ?: "hi"
                Log.d(TAG, "💬 Sending message to: $recipient")
                val response = "Sending message to $recipient: $message"
                conversationManager.addConversationTurn(originalText, response)
                speakText(response)
                conversationManager.endConversation()
            }

            ConversationType.APP_CONTROL -> {
                val appName = params.getParameter("app_name") ?: ""
                Log.d(TAG, "📱 Opening app: $appName")
                val response = "Opening $appName for you."
                conversationManager.addConversationTurn(originalText, response)
                speakText(response)
                // Actually open the app
                openApp(appName)
                conversationManager.endConversation()
            }

            ConversationType.SYSTEM_CONTROL -> {
                val action = params.getParameter("action") ?: ""
                val setting = params.getParameter("setting") ?: ""
                Log.d(TAG, "⚙️ System control: $action $setting")
                // Use LOCAL processing for system control
                executeSystemControl(action, setting, originalText)
                conversationManager.endConversation()
            }

            ConversationType.EXTENDED_CHAT -> {
                val duration = params.getParameter("duration") ?: ""
                Log.d(TAG, "💬 Extended chat request, duration: $duration")

                // Always handle as initial extended chat request
                // The conversation manager will handle continuation logic
                handleExtendedChat(originalText, duration)
            }

            ConversationType.EXIT_COMMAND -> {
                Log.d(TAG, "👋 Exit command detected")
                val response = "Goodbye! Talk to you later."
                conversationManager.addConversationTurn(originalText, response)
                speakText(response)
                conversationManager.endConversation()
            }

            else -> {
                Log.d(TAG, "🤔 Unknown conversation type, using original logic")
                processWithOriginalLogic(originalText, 1.0f)
                conversationManager.endConversation()
            }
        }
    }

    /**
     * Fallback to original processing logic
     */
    private suspend fun processWithOriginalLogic(text: String, confidence: Float) {
        try {
            val voiceCommand = VoiceCommand(
                id = UUID.randomUUID().toString(),
                text = text,
                timestamp = Date(),
                confidence = confidence,
                language = Locale.getDefault().language
            )

            Log.d(TAG, "Processing command: '$text' with local intelligence")

            // Try local processing first
            val localResult = localCommandProcessor.processCommand(voiceCommand)

            when (localResult) {
                is LocalCommandResult.SystemControl -> {
                    Log.d(TAG, "Handling system control locally: ${localResult.action.type}")
                    handleSystemControlLocally(localResult)
                }

                is LocalCommandResult.AppControl -> {
                    Log.d(TAG, "Handling app control locally: ${localResult.action.type}")
                    handleAppControlLocally(localResult)
                }

                is LocalCommandResult.QuickResponse -> {
                    Log.d(TAG, "Providing quick response locally")
                    speakText(localResult.response)
                }

                is LocalCommandResult.RequiresAI -> {
                    Log.d(TAG, "Command requires AI processing: ${localResult.reason}")
                    handleWithAI(voiceCommand)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing recognized text", e)
            updateVoiceState(VoiceState.State.ERROR, errorMessage = e.message)
            speakText("Sorry, something went wrong. Please try again.")
        }
    }

    /**
     * Handle system control commands locally
     */
    private suspend fun handleSystemControlLocally(result: LocalCommandResult.SystemControl) {
        try {
            // Execute the system action through system control manager
            val success = systemControlManager.executeSystemAction(result.action)

            if (success) {
                speakText(result.response)
            } else {
                speakText("I couldn't execute that system command. Please check permissions.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing system control", e)
            speakText("Sorry, I couldn't execute that system command.")
        }
    }

    /**
     * Handle app control commands locally
     */
    private suspend fun handleAppControlLocally(result: LocalCommandResult.AppControl) {
        try {
            // Execute the app action through system control manager
            val success = systemControlManager.executeSystemAction(result.action)

            if (success) {
                speakText(result.response)
            } else {
                speakText("I couldn't execute that app command. Please check permissions.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing app control", e)
            speakText("Sorry, I couldn't execute that app command.")
        }
    }

    /**
     * Handle complex commands with AI
     */
    private suspend fun handleWithAI(voiceCommand: VoiceCommand) {
        try {
            val result = processVoiceCommandUseCase(voiceCommand)
            
            result.onSuccess { response ->
                Log.d(TAG, "AI Response: ${response.text}")
                speakText(response.text)
            }.onFailure { error ->
                Log.e(TAG, "Failed to process command with AI", error)
                speakText("Sorry, I couldn't process your request. Please try again.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing with AI", e)
            speakText("Sorry, something went wrong with AI processing.")
        }
    }

    private fun initializeTextToSpeech() {
        textToSpeech = TextToSpeech(this, this)
    }

    private fun initializeVoiceManager() {
        // Initialize Azure-Only STT service in background thread
        serviceScope.launch(Dispatchers.IO) {
            val success = azureOnlySTTService.initialize()
            withContext(Dispatchers.Main) {
                if (success) {
                    Log.d(TAG, "Azure-Only STT initialized successfully")
                } else {
                    Log.e(TAG, "Failed to initialize Azure-Only STT")
                }
            }
        }

        // System control manager is injected and ready to use
    }

    /**
     * Create STT listener for Azure Speech Services
     */
    private fun createSTTListener(): AzureOnlySTTService.STTListener {
        return object : AzureOnlySTTService.STTListener {
            override fun onReady() {
                Log.d(TAG, "Azure STT ready")
            }

            override fun onListeningStarted() {
                Log.d(TAG, "Azure STT listening started")
                isListening = true
                updateVoiceState(VoiceState.State.LISTENING_COMMAND, isListening = true)
            }

            override fun onPartialResult(text: String) {
                Log.d(TAG, "Azure partial result: '$text'")
                // Could update UI with partial results
            }

            override fun onFinalResult(text: String) {
                Log.d(TAG, "🎯 AdvancedVoiceProcessing onFinalResult() called with: '$text'")
                if (text.isNotBlank()) {
                    Log.d(TAG, "✅ Azure Speech recognized: '$text'")
                    cancelListeningTimeout()
                    Log.d(TAG, "🔄 Processing recognized text...")
                    processRecognizedText(text, 1.0f)
                } else {
                    Log.w(TAG, "⚠️ Empty recognition result")
                    stopListening()
                }
            }

            override fun onListeningStopped() {
                Log.d(TAG, "Azure STT listening stopped")
                isListening = false
                updateVoiceState(VoiceState.State.IDLE, isListening = false)
            }

            override fun onError(error: String) {
                Log.e(TAG, "Azure STT error: $error")
                isListening = false
                updateVoiceState(VoiceState.State.ERROR, errorMessage = error)
                stopListening()
            }
        }
    }

    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            textToSpeech?.let { tts ->
                val result = tts.setLanguage(Locale.getDefault())
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.e(TAG, "Language not supported")
                } else {
                    isTtsInitialized = true
                    tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                        override fun onStart(utteranceId: String?) {
                            Log.d(TAG, "🎤 TTS onStart called with utteranceId: $utteranceId")
                            updateVoiceState(VoiceState.State.SPEAKING_RESPONSE, isSpeaking = true)
                        }

                        override fun onDone(utteranceId: String?) {
                            Log.d(TAG, "🎤 TTS onDone called with utteranceId: $utteranceId")
                            isSpeaking = false

                            // Check if we're in an active conversation (regardless of utterance ID)
                            val isInConversation = conversationManager.isInConversation()
                            val currentState = conversationManager.getCurrentState()

                            Log.d(TAG, "🎤 TTS finished - In conversation: $isInConversation, State: $currentState")

                            // If we're in any active conversation, continue listening
                            if (isInConversation && (currentState == ConversationState.WAITING_FOR_CLARIFICATION ||
                                                   currentState == ConversationState.PROCESSING ||
                                                   currentState == ConversationState.EXECUTING)) {
                                Log.d(TAG, "🔄 Conversational response finished, restarting listening...")
                                Log.d(TAG, "📊 Current conversation state: ${conversationManager.getCurrentState()}")
                                Log.d(TAG, "📊 Is in conversation: ${conversationManager.isInConversation()}")

                                // Universal conversation continuation for ALL types
                                if (conversationManager.isInConversation()) {
                                    val currentState = conversationManager.getCurrentState()
                                    if (currentState == ConversationState.WAITING_FOR_CLARIFICATION ||
                                        currentState == ConversationState.LISTENING ||
                                        currentState == ConversationState.PROCESSING) {

                                        Log.d(TAG, "🎤 Restarting listening for continued conversation (${currentState})...")
                                        // Restart listening for ANY conversation type
                                        serviceScope.launch {
                                            delay(500) // Brief pause before listening again
                                            // Cancel any existing timeouts before restarting
                                            cancelListeningTimeout()
                                            startListening()
                                        }
                                    } else {
                                        Log.d(TAG, "🏁 Conversation state indicates end: $currentState")
                                        updateVoiceState(VoiceState.State.IDLE, isSpeaking = false)
                                        stopSelf()
                                    }
                                } else {
                                    Log.d(TAG, "🏁 No active conversation, ending service")
                                    updateVoiceState(VoiceState.State.IDLE, isSpeaking = false)
                                    stopSelf()
                                }
                            } else {
                                // Regular response, end service
                                updateVoiceState(VoiceState.State.IDLE, isSpeaking = false)
                                stopSelf()
                            }
                        }

                        override fun onError(utteranceId: String?) {
                            Log.e(TAG, "🎤 TTS onError called with utteranceId: $utteranceId")
                            updateVoiceState(VoiceState.State.ERROR, errorMessage = "TTS error")
                            isSpeaking = false

                            // End conversation on TTS error
                            conversationManager.endConversation()
                            stopSelf()
                        }
                    })
                    Log.d(TAG, "TTS initialized successfully")
                }
            }
        } else {
            Log.e(TAG, "TTS initialization failed")
        }
    }

    private fun speakText(text: String) {
        if (!isTtsInitialized) {
            Log.e(TAG, "TTS not initialized")
            return
        }

        try {
            startForeground(NOTIFICATION_ID, createNotification("Speaking response..."))
            isSpeaking = true
            updateVoiceState(VoiceState.State.SPEAKING_RESPONSE, isSpeaking = true)

            val utteranceId = UUID.randomUUID().toString()
            textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, null, utteranceId)

            Log.d(TAG, "Speaking: $text")
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text", e)
            updateVoiceState(VoiceState.State.ERROR, errorMessage = e.message)
        }
    }

    /**
     * Speak text and automatically continue listening for conversational mode
     */
    private fun speakTextAndContinueListening(text: String) {
        if (!isTtsInitialized) {
            Log.e(TAG, "TTS not initialized")
            return
        }

        try {
            startForeground(NOTIFICATION_ID, createNotification("Speaking and listening..."))
            isSpeaking = true
            updateVoiceState(VoiceState.State.SPEAKING_RESPONSE, isSpeaking = true)

            val utteranceId = "conversational_${System.currentTimeMillis()}"
            Log.d(TAG, "🎤 Setting up TTS with utterance ID: $utteranceId")
            textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, null, utteranceId)

            Log.d(TAG, "🗣️ Speaking and will continue listening: $text")
            Log.d(TAG, "🔄 TTS callback should trigger when speaking finishes")
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text for conversation", e)
            updateVoiceState(VoiceState.State.ERROR, errorMessage = e.message)
        }
    }

    /**
     * Check if topic should use built-in responses vs AI
     */
    private fun isBuiltInTopic(topic: String): Boolean {
        val lowerTopic = topic.lowercase().trim()

        // Only very basic topics about Zara herself should use built-in responses
        // Everything else should use AI/web search for up-to-date information
        return lowerTopic.contains("you") || lowerTopic.contains("zara") || lowerTopic.contains("yourself") ||
               lowerTopic.contains("your name") || lowerTopic.contains("who are you") ||
               lowerTopic.contains("capabilities") || lowerTopic.contains("what can you do")
    }

    /**
     * Query AI for information about a topic
     */
    private fun queryAIForInformation(topic: String, originalText: String) {
        serviceScope.launch {
            try {
                Log.d(TAG, "🤖 Starting AI query for topic: $topic")

                // Create a VoiceCommand for AI processing
                val voiceCommand = VoiceCommand(
                    id = UUID.randomUUID().toString(),
                    text = "Tell me about $topic",
                    timestamp = Date(),
                    confidence = 1.0f,
                    language = Locale.getDefault().language
                )

                // Use AI processing with conversational context
                handleWithAIForConversation(voiceCommand, originalText, topic)

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error querying AI for information", e)
                val fallbackResponse = "I'm sorry, I couldn't get information about $topic right now. Please try again later."
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakText(fallbackResponse)
                conversationManager.endConversation()
            }
        }
    }

    /**
     * Handle chat continuation (user's response in ongoing conversation)
     */
    private fun handleChatContinuation(userInput: String) {
        serviceScope.launch {
            try {
                Log.d(TAG, "💬 Handling chat continuation: '$userInput'")

                // Create a VoiceCommand for AI processing
                val voiceCommand = VoiceCommand(
                    id = UUID.randomUUID().toString(),
                    text = userInput,
                    timestamp = Date(),
                    confidence = 1.0f,
                    language = Locale.getDefault().language
                )

                // Process with AI and continue the conversation
                handleExtendedChatWithAI(voiceCommand, userInput, "")

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error in chat continuation", e)
                val fallbackResponse = "That's interesting! Tell me more."
                conversationManager.addConversationTurn(userInput, fallbackResponse)
                speakTextAndContinueListening(fallbackResponse)
            }
        }
    }

    /**
     * Handle extended chat conversations
     */
    private fun handleExtendedChat(originalText: String, duration: String) {
        serviceScope.launch {
            try {
                Log.d(TAG, "💬 Starting extended chat session")

                // Create a chat-optimized prompt
                val chatPrompt = if (duration.isNotEmpty()) {
                    "Let's have a conversation for $duration! I'm excited to chat with you."
                } else {
                    "I'd love to chat with you! What's on your mind?"
                }

                // Create a VoiceCommand for AI processing
                val voiceCommand = VoiceCommand(
                    id = UUID.randomUUID().toString(),
                    text = originalText,
                    timestamp = Date(),
                    confidence = 1.0f,
                    language = Locale.getDefault().language
                )

                // Cancel any existing timeouts and set up extended chat timeout
                cancelListeningTimeout()
                Log.d(TAG, "⏰ Cancelled listening timeout for extended chat")

                // Set up conversation state for extended chat
                conversationManager.conversationState.value?.let { context ->
                    val updatedContext = context.copy(
                        state = ConversationState.WAITING_FOR_CLARIFICATION,
                        type = ConversationType.EXTENDED_CHAT
                    )
                    conversationManager.updateContext(updatedContext)
                    Log.d(TAG, "💬 Extended chat conversation state set up")
                }

                // Start extended chat timeout (2 minutes)
                startExtendedChatTimeout()

                // Process with AI but keep conversation active
                handleExtendedChatWithAI(voiceCommand, originalText, chatPrompt)

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error in extended chat", e)
                val fallbackResponse = "I'd love to chat with you! What would you like to talk about?"
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakTextAndContinueListening(fallbackResponse)
            }
        }
    }

    /**
     * Handle AI processing for extended chat with continuous listening
     */
    private suspend fun handleExtendedChatWithAI(voiceCommand: VoiceCommand, originalText: String, chatPrompt: String) {
        try {
            Log.d(TAG, "🤖 Processing extended chat with AI")

            val result = processVoiceCommandUseCase(voiceCommand)

            result.onSuccess { response ->
                Log.d(TAG, "✅ AI Chat Response received: ${response.text}")

                // Add to conversation history
                conversationManager.addConversationTurn(originalText, response.text)

                // Speak the AI response and continue listening for more conversation
                speakTextAndContinueListening(response.text)

                // Set conversation state to keep it active
                conversationManager.conversationState.value?.let { context ->
                    val updatedContext = context.copy(
                        state = ConversationState.WAITING_FOR_CLARIFICATION,
                        type = ConversationType.EXTENDED_CHAT
                    )
                    // Update the conversation context to keep it active
                    conversationManager.updateContext(updatedContext)
                }

                // Keep conversation active - don't end it
                Log.d(TAG, "💬 Extended chat session active, waiting for user response...")

            }.onFailure { error ->
                Log.e(TAG, "❌ Failed to process extended chat", error)
                val fallbackResponse = "I'd love to chat with you! What would you like to talk about?"
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakTextAndContinueListening(fallbackResponse)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in extended chat AI processing", e)
            val fallbackResponse = "I'm here to chat! What's on your mind?"
            conversationManager.addConversationTurn(originalText, fallbackResponse)
            speakTextAndContinueListening(fallbackResponse)
        }
    }

    /**
     * Handle AI processing specifically for conversational information requests
     */
    private suspend fun handleWithAIForConversation(voiceCommand: VoiceCommand, originalText: String, topic: String) {
        try {
            Log.d(TAG, "🤖 Processing AI request for conversational topic: $topic")

            val result = processVoiceCommandUseCase(voiceCommand)

            result.onSuccess { response ->
                Log.d(TAG, "✅ AI Response received: ${response.text}")

                // Add to conversation history
                conversationManager.addConversationTurn(originalText, response.text)

                // Speak the AI response
                speakText(response.text)

                // End the conversation
                conversationManager.endConversation()

            }.onFailure { error ->
                Log.e(TAG, "❌ Failed to process AI request for topic: $topic", error)
                val fallbackResponse = "I'm sorry, I couldn't get information about $topic right now. Please try again later."
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakText(fallbackResponse)
                conversationManager.endConversation()
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in AI conversation processing", e)
            val fallbackResponse = "I'm sorry, something went wrong while getting information about $topic."
            conversationManager.addConversationTurn(originalText, fallbackResponse)
            speakText(fallbackResponse)
            conversationManager.endConversation()
        }
    }

    /**
     * Generate intelligent response for built-in information requests
     */
    private fun generateInformationResponse(topic: String): String {
        val lowerTopic = topic.lowercase().trim()

        return when {
            // Self-identification
            lowerTopic.contains("you") || lowerTopic.contains("zara") || lowerTopic.contains("yourself") -> {
                "I'm Zara, your AI voice assistant. I can help you control your device, answer questions, play music, send messages, and much more. Just ask me anything!"
            }

            // Weather - suggest opening weather app
            lowerTopic.contains("weather") -> {
                "I'd love to help you with weather information. Let me open a weather app for you to get current conditions."
            }

            // Time
            lowerTopic.contains("time") || lowerTopic.contains("clock") -> {
                val currentTime = java.text.SimpleDateFormat("h:mm a", java.util.Locale.getDefault()).format(java.util.Date())
                "The current time is $currentTime."
            }

            // Date
            lowerTopic.contains("date") || lowerTopic.contains("today") -> {
                val currentDate = java.text.SimpleDateFormat("EEEE, MMMM d, yyyy", java.util.Locale.getDefault()).format(java.util.Date())
                "Today is $currentDate."
            }

            // Device capabilities
            lowerTopic.contains("device") || lowerTopic.contains("phone") || lowerTopic.contains("capabilities") -> {
                "I can help you control your device in many ways: turn WiFi and Bluetooth on or off, open apps, send messages, play music, take screenshots, and much more. What would you like me to help you with?"
            }

            // Apps
            lowerTopic.contains("app") || lowerTopic.contains("application") -> {
                "I can open any app on your device. Just say 'open' followed by the app name, like 'open WhatsApp' or 'open camera'. I can also help you with app-specific tasks."
            }

            // Music
            lowerTopic.contains("music") || lowerTopic.contains("song") -> {
                "I can help you play music! Just say 'play' followed by a song name, artist, or just 'play music' to start your default music app."
            }

            // Messages
            lowerTopic.contains("message") || lowerTopic.contains("text") || lowerTopic.contains("sms") -> {
                "I can help you send messages! Just say 'send message to' followed by the contact name, and I'll help you compose and send it."
            }

            // Battery
            lowerTopic.contains("battery") -> {
                try {
                    val batteryManager = getSystemService(Context.BATTERY_SERVICE) as android.os.BatteryManager
                    val batteryLevel = batteryManager.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CAPACITY)
                    "Your device battery is currently at $batteryLevel percent."
                } catch (e: Exception) {
                    "I can't access battery information right now, but you can check it in your device settings."
                }
            }

            // Storage
            lowerTopic.contains("storage") || lowerTopic.contains("memory") -> {
                "I can help you check storage information. Would you like me to open your device storage settings?"
            }

            // Network/Internet
            lowerTopic.contains("network") || lowerTopic.contains("internet") || lowerTopic.contains("connection") -> {
                "I can help you with network settings. I can turn WiFi on or off, check Bluetooth, or open network settings for you."
            }

            // General topics - use AI for everything else
            else -> {
                // This shouldn't be reached since we filter built-in topics, but just in case
                "Let me search for information about $topic for you."
            }
        }
    }

    /**
     * Open an app by name
     */
    private fun openApp(appName: String) {
        try {
            Log.d(TAG, "📱 Attempting to open app: $appName")

            // Common app mappings
            val appPackages = mapOf(
                "whatsapp" to "com.whatsapp",
                "telegram" to "org.telegram.messenger",
                "instagram" to "com.instagram.android",
                "facebook" to "com.facebook.katana",
                "spotify" to "com.spotify.music",
                "gmail" to "com.google.android.gm",
                "chrome" to "com.android.chrome",
                "maps" to "com.google.android.apps.maps",
                "camera" to "com.android.camera",
                "gallery" to "com.android.gallery3d",
                "settings" to "com.android.settings",
                "calculator" to "com.android.calculator2",
                "calendar" to "com.android.calendar",
                "contacts" to "com.android.contacts"
            )

            val packageName = appPackages[appName.lowercase()]

            if (packageName != null) {
                val intent = packageManager.getLaunchIntentForPackage(packageName)
                if (intent != null) {
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                    Log.d(TAG, "✅ Successfully opened $appName")
                } else {
                    Log.w(TAG, "⚠️ App $appName not found")
                }
            } else {
                // Try to find app by searching installed apps
                val intent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory(Intent.CATEGORY_LAUNCHER)
                }
                val apps = packageManager.queryIntentActivities(intent, 0)

                val matchingApp = apps.find {
                    it.loadLabel(packageManager).toString().lowercase().contains(appName.lowercase())
                }

                if (matchingApp != null) {
                    val launchIntent = Intent().apply {
                        setClassName(matchingApp.activityInfo.packageName, matchingApp.activityInfo.name)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(launchIntent)
                    Log.d(TAG, "✅ Successfully opened $appName via search")
                } else {
                    Log.w(TAG, "⚠️ Could not find app: $appName")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening app: $appName", e)
        }
    }

    /**
     * Execute system control commands locally
     */
    private fun executeSystemControl(action: String, setting: String, originalText: String) {
        val startTime = System.currentTimeMillis()
        try {
            Log.d(TAG, "⚙️ Executing system control: $action $setting")

            // Get value parameter if present
            val params = conversationManager.currentContext?.parameters
            val value = params?.getParameter("value")

            val response = when (setting.lowercase()) {
                "wifi", "wi-fi" -> {
                    if (action.contains("on") || action.contains("enable")) {
                        "Turning on WiFi for you."
                    } else {
                        "Turning off WiFi for you."
                    }
                }
                "bluetooth" -> {
                    if (action.contains("on") || action.contains("enable")) {
                        "Turning on Bluetooth for you."
                    } else {
                        "Turning off Bluetooth for you."
                    }
                }
                "flashlight", "torch" -> {
                    if (action.contains("on") || action.contains("enable")) {
                        "Turning on flashlight for you."
                    } else {
                        "Turning off flashlight for you."
                    }
                }
                "volume" -> {
                    if (value != null) {
                        "Setting volume to $value percent."
                    } else if (action.contains("on") || action.contains("enable")) {
                        "Turning volume on for you."
                    } else {
                        "I'll help you with volume settings."
                    }
                }
                "brightness" -> {
                    if (value != null) {
                        "Setting brightness to $value percent."
                    } else {
                        "I'll help you with brightness settings."
                    }
                }
                "time" -> {
                    val currentTime = java.text.SimpleDateFormat("h:mm a", java.util.Locale.getDefault())
                        .format(java.util.Date())
                    "The current time is $currentTime."
                }
                "date" -> {
                    val currentDate = java.text.SimpleDateFormat("EEEE, MMMM d, yyyy", java.util.Locale.getDefault())
                        .format(java.util.Date())
                    "Today is $currentDate."
                }
                else -> {
                    "I'll help you with $setting settings."
                }
            }

            conversationManager.addConversationTurn(originalText, response)
            speakText(response)

            // Send broadcast to actually execute the system control
            val intent = Intent("com.zara.assistant.SYSTEM_CONTROL").apply {
                putExtra("action", action)
                putExtra("setting", setting)
                if (value != null) {
                    putExtra("value", value)
                }
            }
            sendBroadcast(intent)

            // Log interaction for learning
            logUserInteraction(originalText, response, true, System.currentTimeMillis() - startTime)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error executing system control", e)
            val errorResponse = "I'm having trouble with that system setting right now."
            conversationManager.addConversationTurn(originalText, errorResponse)
            speakText(errorResponse)

            // Log failed interaction
            logUserInteraction(originalText, errorResponse, false, System.currentTimeMillis() - startTime)
        }
    }

    /**
     * Handle information requests using AI Orchestration Service
     */
    private fun handleInformationRequestWithAI(originalText: String, topic: String) {
        serviceScope.launch {
            try {
                Log.d(TAG, "🧠 Processing information request with AI: $topic")

                val sessionId = conversationManager.currentContext?.conversationId ?: "default"
                val conversationContext = buildConversationContext()

                val aiResponse = aiOrchestrationService.processUserInput(
                    userInput = originalText,
                    conversationContext = conversationContext,
                    sessionId = sessionId
                )

                // Speak the AI response
                conversationManager.addConversationTurn(originalText, aiResponse.text)
                speakText(aiResponse.text)

                // Handle proactive suggestions if any
                if (aiResponse.suggestions.isNotEmpty()) {
                    Log.d(TAG, "💡 AI provided ${aiResponse.suggestions.size} suggestions")
                    // Could display suggestions in UI or speak them
                }

                conversationManager.endConversation()

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error processing information request with AI", e)
                val fallbackResponse = "I'm having trouble finding information about that right now."
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakText(fallbackResponse)
                conversationManager.endConversation()
            }
        }
    }

    /**
     * Handle general chat using AI Orchestration Service
     */
    private fun handleGeneralChatWithAI(originalText: String) {
        serviceScope.launch {
            try {
                Log.d(TAG, "🧠 Processing general chat with AI")

                val sessionId = conversationManager.currentContext?.conversationId ?: "default"
                val conversationContext = buildConversationContext()

                val aiResponse = aiOrchestrationService.processUserInput(
                    userInput = originalText,
                    conversationContext = conversationContext,
                    sessionId = sessionId
                )

                conversationManager.addConversationTurn(originalText, aiResponse.text)
                speakText(aiResponse.text)
                conversationManager.endConversation()

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error processing general chat with AI", e)
                val fallbackResponse = "I understand. How else can I help you?"
                conversationManager.addConversationTurn(originalText, fallbackResponse)
                speakText(fallbackResponse)
                conversationManager.endConversation()
            }
        }
    }

    /**
     * Build conversation context for AI processing
     */
    private fun buildConversationContext(): String {
        return try {
            val context = conversationManager.currentContext
            val history = context?.conversationHistory?.takeLast(5)?.joinToString("\n") { turn ->
                "User: ${turn.userInput}\nZara: ${turn.zaraResponse}"
            } ?: ""

            "Recent conversation:\n$history"
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error building conversation context", e)
            ""
        }
    }

    /**
     * Log user interaction for learning
     */
    private fun logUserInteraction(command: String, response: String, success: Boolean, executionTime: Long) {
        try {
            val intent = Intent(this, UserLearningService::class.java).apply {
                action = UserLearningService.ACTION_LOG_INTERACTION
                putExtra(UserLearningService.EXTRA_COMMAND, command)
                putExtra(UserLearningService.EXTRA_RESPONSE, response)
                putExtra(UserLearningService.EXTRA_SUCCESS, success)
                putExtra(UserLearningService.EXTRA_EXECUTION_TIME, executionTime)
            }
            startService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error logging interaction for learning", e)
        }
    }

    private fun updateVoiceState(
        state: VoiceState.State,
        isListening: Boolean = false,
        isProcessing: Boolean = false,
        isSpeaking: Boolean = false,
        currentCommand: String? = null,
        errorMessage: String? = null,
        confidence: Float = 0f
    ) {
        _voiceState.value = _voiceState.value.copy(
            currentState = state,
            isListening = isListening,
            isProcessing = isProcessing,
            isSpeaking = isSpeaking,
            lastActivity = Date(),
            currentCommand = currentCommand,
            errorMessage = errorMessage,
            confidence = confidence
        )
    }

    private fun createNotification(contentText: String): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, ZaraApplication.VOICE_PROCESSING_CHANNEL_ID)
            .setContentTitle("Zara is processing")
            .setContentText(contentText)
            .setSmallIcon(R.drawable.ic_voice_assistant)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🔥 onDestroy() called - Service being destroyed")
        Log.d(TAG, "🔥 Current conversation state: ${conversationManager.getCurrentState()}")
        Log.d(TAG, "🔥 Is in conversation: ${conversationManager.isInConversation()}")
        Log.d(TAG, "🔥 Is speaking: $isSpeaking")
        Log.d(TAG, "🔥 Is listening: $isListening")

        cancelListeningTimeout()
        azureOnlySTTService.destroy()
        textToSpeech?.shutdown()
        serviceScope.cancel()
        Log.d(TAG, "Advanced Voice Processing Service destroyed")
    }
}
