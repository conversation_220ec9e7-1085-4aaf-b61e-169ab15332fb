package com.zara.assistant.domain.usecase

import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.AIResponse
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.domain.repository.VoiceRepository
import com.zara.assistant.domain.repository.AIRepository
import com.zara.assistant.domain.repository.ConversationRepository
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for processing voice commands end-to-end
 */
@Singleton
class ProcessVoiceCommandUseCase @Inject constructor(
    private val voiceRepository: VoiceRepository,
    private val aiRepository: AIRepository,
    private val conversationRepository: ConversationRepository
) {
    
    suspend operator fun invoke(command: VoiceCommand): Result<AIResponse> {
        return try {
            // Update voice state to processing
            voiceRepository.setVoiceState(VoiceState.State.PROCESSING_COMMAND)
            
            // Get active conversation or create new one
            val conversation = conversationRepository.getActiveConversation()
                ?: conversationRepository.createConversation().getOrThrow()
            
            // Add user command to conversation
            conversationRepository.addUserCommand(conversation.id, command)
            
            // Get conversation history for context
            val history = conversationRepository.getConversationHistory(conversation.id)
            
            // Update state to generating response
            voiceRepository.setVoiceState(VoiceState.State.GENERATING_RESPONSE)
            
            // Process command with AI
            val response = aiRepository.processCommand(command).getOrThrow()
            
            // Add AI response to conversation
            conversationRepository.addAIResponse(conversation.id, response)
            
            // Update voice state to speaking
            voiceRepository.setVoiceState(VoiceState.State.SPEAKING_RESPONSE)
            
            // Speak the response
            voiceRepository.speak(response.text)
            
            // Execute any system actions
            if (response.actions.isNotEmpty()) {
                voiceRepository.setVoiceState(VoiceState.State.EXECUTING_ACTION)
                executeSystemActions(response.actions)
            }
            
            // Return to idle state
            voiceRepository.setVoiceState(VoiceState.State.IDLE)
            
            Result.success(response)
            
        } catch (e: Exception) {
            // Handle error
            voiceRepository.setVoiceState(VoiceState.State.ERROR)
            val errorResponse = aiRepository.handleAPIError(e, command)
            voiceRepository.speak(errorResponse.text)
            Result.failure(e)
        }
    }
    
    private suspend fun executeSystemActions(actions: List<com.zara.assistant.domain.model.SystemAction>) {
        // Implementation for executing system actions
        // This will be handled by the SystemControlService
        actions.forEach { action ->
            when (action.type) {
                com.zara.assistant.domain.model.ActionType.OPEN_APP -> {
                    // Open app logic
                }
                com.zara.assistant.domain.model.ActionType.TOGGLE_WIFI -> {
                    // Toggle WiFi logic
                }
                // Add other action types
                else -> {
                    // Handle unknown actions
                }
            }
        }
    }
}
