package com.zara.assistant.di

import com.zara.assistant.BuildConfig
import com.zara.assistant.core.Constants
import com.zara.assistant.data.remote.api.CohereApiService
import com.zara.assistant.data.remote.api.PerplexityApiService
import com.zara.assistant.data.remote.interceptor.AuthInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * Dependency injection module for network-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }

    @Provides
    @Singleton
    @Named("cohere_auth")
    fun provideCohereAuthInterceptor(): AuthInterceptor {
        return AuthInterceptor(
            headerName = "Authorization",
            headerValue = "Bearer OkmxGVOm5PiciSYdRGeC7n6EhHpamiASce5aAbwh"
        )
    }

    @Provides
    @Singleton
    @Named("perplexity_auth")
    fun providePerplexityAuthInterceptor(): AuthInterceptor {
        return AuthInterceptor(
            headerName = "Authorization",
            headerValue = "Bearer plx-ZugJI0TK68UX6jYyg3QHYmhKJZThtQRWvAiZ3kgQ9YndxlF7"
        )
    }

    @Provides
    @Singleton
    fun provideOkHttpClient(loggingInterceptor: HttpLoggingInterceptor): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(Constants.API.CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(Constants.API.READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(Constants.API.REQUEST_TIMEOUT, TimeUnit.SECONDS)
            .build()
    }

    @Provides
    @Singleton
    @Named("cohere_client")
    fun provideCohereOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        @Named("cohere_auth") authInterceptor: AuthInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(Constants.API.CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(Constants.API.READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(Constants.API.REQUEST_TIMEOUT, TimeUnit.SECONDS)
            .build()
    }

    @Provides
    @Singleton
    @Named("perplexity_client")
    fun providePerplexityOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        @Named("perplexity_auth") authInterceptor: AuthInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(authInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(Constants.API.CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(Constants.API.READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(Constants.API.REQUEST_TIMEOUT, TimeUnit.SECONDS)
            .build()
    }

    @Provides
    @Singleton
    @Named("cohere_retrofit")
    fun provideCohereRetrofit(@Named("cohere_client") okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(Constants.API.COHERE_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    @Named("perplexity_retrofit")
    fun providePerplexityRetrofit(@Named("perplexity_client") okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(Constants.API.PERPLEXITY_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    fun provideCohereApiService(@Named("cohere_retrofit") retrofit: Retrofit): CohereApiService {
        return retrofit.create(CohereApiService::class.java)
    }

    @Provides
    @Singleton
    fun providePerplexityApiService(@Named("perplexity_retrofit") retrofit: Retrofit): PerplexityApiService {
        return retrofit.create(PerplexityApiService::class.java)
    }
}
