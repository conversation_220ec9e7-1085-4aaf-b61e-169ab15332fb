package com.zara.assistant.data.remote.dto

/**
 * Cohere API request DTOs
 */
data class CohereGenerateRequest(
    val model: String = "command",
    val prompt: String,
    val max_tokens: Int = 300,
    val temperature: Double = 0.7,
    val k: Int = 0,
    val stop_sequences: List<String> = emptyList(),
    val return_likelihoods: String = "NONE"
)

data class CohereClassifyRequest(
    val model: String = "embed-english-v2.0",
    val inputs: List<String>,
    val examples: List<CohereExample>
)

data class CohereExample(
    val text: String,
    val label: String
)

data class CohereSummarizeRequest(
    val text: String,
    val length: String = "medium",
    val format: String = "paragraph",
    val model: String = "summarize-medium",
    val extractiveness: String = "medium",
    val temperature: Double = 0.3
)
