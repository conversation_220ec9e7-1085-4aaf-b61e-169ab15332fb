package com.zara.assistant.services

import android.app.admin.DevicePolicyManager
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.media.session.MediaController
import android.media.session.MediaSessionManager
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.annotation.RequiresApi
import com.zara.assistant.domain.model.ActionType
import com.zara.assistant.domain.model.SystemAction
import com.zara.assistant.services.ZaraDeviceAdminReceiver
import com.zara.assistant.ui.RestrictedSettingsGuideActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import java.lang.reflect.Method
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Smart System Control Manager for executing system actions
 * Integrates with ZaraAccessibilityService and system APIs
 * Provides intelligent WiFi, Bluetooth, and other system controls
 */
@Singleton
class SystemControlManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "SystemControlManager"
        private const val SMART_TOGGLE_DELAY = 1500L // Delay for smart toggle operations
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private val audioManager: AudioManager by lazy {
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    private val wifiManager: WifiManager by lazy {
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }

    private val bluetoothManager: BluetoothManager by lazy {
        context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }

    private val bluetoothAdapter: BluetoothAdapter? by lazy {
        bluetoothManager.adapter
    }

    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private val devicePolicyManager: DevicePolicyManager by lazy {
        context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    }

    private val deviceAdminComponent: ComponentName by lazy {
        ComponentName(context, ZaraDeviceAdminReceiver::class.java)
    }

    /**
     * Execute a system action
     */
    fun executeSystemAction(action: SystemAction): Boolean {
        return try {
            Log.d(TAG, "🎯 Executing system action: ${action.type}")
            Log.d(TAG, "📋 Action parameters: ${action.parameters}")

            val result = when (action.type) {
                // Volume controls
                ActionType.ADJUST_VOLUME -> adjustVolume(action.parameters["direction"] ?: "up")
                ActionType.SET_VOLUME -> setVolume(action.parameters["level"]?.toIntOrNull() ?: 50)
                ActionType.MUTE_VOLUME -> muteVolume()
                
                // System settings (smart toggling)
                ActionType.TOGGLE_WIFI -> smartToggleWifi(action.parameters["state"])
                ActionType.TOGGLE_BLUETOOTH -> smartToggleBluetooth(action.parameters["state"])
                ActionType.SET_BRIGHTNESS -> setBrightness(action.parameters["value"])
                ActionType.ADJUST_BRIGHTNESS -> adjustBrightness(action.parameters["direction"])
                
                // Navigation actions (requires accessibility service)
                ActionType.GO_HOME -> performGlobalAction("home")
                ActionType.GO_BACK -> performGlobalAction("back")
                ActionType.OPEN_RECENT_APPS -> performGlobalAction("recents")
                ActionType.OPEN_NOTIFICATIONS -> performGlobalAction("notifications")
                ActionType.OPEN_QUICK_SETTINGS -> performGlobalAction("quick_settings")
                ActionType.TAKE_SCREENSHOT -> performGlobalAction("screenshot")
                
                // App management (requires accessibility service)
                ActionType.OPEN_APP -> {
                    val appName = action.target ?: "unknown"
                    Log.d(TAG, "🎯 Opening app with target: '$appName'")
                    openApp(appName)
                }
                ActionType.CLOSE_APP -> closeApp()
                ActionType.SWITCH_APP -> switchToApp(action.target)

                // Music controls
                ActionType.PLAY_MUSIC -> playMusic(action.parameters)
                ActionType.PAUSE_MUSIC -> pauseMusic()
                ActionType.STOP_MUSIC -> stopMusic()
                ActionType.NEXT_TRACK -> nextTrack()
                ActionType.PREVIOUS_TRACK -> previousTrack()

                // Settings access
                ActionType.OPEN_SETTINGS -> openSettings()
                ActionType.OPEN_WIFI_SETTINGS -> openWifiSettings()
                ActionType.OPEN_BLUETOOTH_SETTINGS -> openBluetoothSettings()
                ActionType.OPEN_DISPLAY_SETTINGS -> openDisplaySettings()
                ActionType.OPEN_SOUND_SETTINGS -> openSoundSettings()
                ActionType.OPEN_SECURITY_SETTINGS -> openSecuritySettings()
                ActionType.OPEN_ACCESSIBILITY_SETTINGS -> openAccessibilitySettings()

                // Additional smart system controls
                ActionType.TOGGLE_AIRPLANE_MODE -> smartToggleAirplaneMode(action.parameters["state"])
                ActionType.TOGGLE_MOBILE_DATA -> smartToggleMobileData(action.parameters["state"])
                ActionType.TOGGLE_HOTSPOT -> smartToggleHotspot(action.parameters["state"])
                ActionType.TOGGLE_NFC -> smartToggleNFC(action.parameters["state"])
                ActionType.TOGGLE_LOCATION -> smartToggleLocation(action.parameters["state"])
                ActionType.TOGGLE_AUTO_ROTATE -> smartToggleAutoRotate(action.parameters["state"])
                ActionType.TOGGLE_DO_NOT_DISTURB -> smartToggleDoNotDisturb(action.parameters["state"])

                else -> {
                    Log.w(TAG, "⚠️ Unsupported action type: ${action.type}")
                    false
                }
            }

            Log.d(TAG, if (result) "✅ System action completed successfully" else "❌ System action failed")
            result

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error executing system action: ${action.type}", e)
            false
        }
    }

    /**
     * Adjust volume up or down
     */
    private fun adjustVolume(direction: String): Boolean {
        return try {
            val flag = when (direction.lowercase()) {
                "up" -> AudioManager.ADJUST_RAISE
                "down" -> AudioManager.ADJUST_LOWER
                else -> AudioManager.ADJUST_RAISE
            }
            
            audioManager.adjustStreamVolume(
                AudioManager.STREAM_MUSIC,
                flag,
                AudioManager.FLAG_SHOW_UI
            )
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adjusting volume", e)
            false
        }
    }

    /**
     * Set volume to specific level (0-100)
     */
    private fun setVolume(level: Int): Boolean {
        return try {
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
            val targetVolume = (level * maxVolume) / 100
            
            audioManager.setStreamVolume(
                AudioManager.STREAM_MUSIC,
                targetVolume,
                AudioManager.FLAG_SHOW_UI
            )
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error setting volume", e)
            false
        }
    }

    /**
     * Mute/unmute volume
     */
    private fun muteVolume(): Boolean {
        return try {
            audioManager.adjustStreamVolume(
                AudioManager.STREAM_MUSIC,
                AudioManager.ADJUST_TOGGLE_MUTE,
                AudioManager.FLAG_SHOW_UI
            )
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error muting volume", e)
            false
        }
    }

    /**
     * Set brightness to specific level (0-100)
     */
    private fun setBrightness(value: String?): Boolean {
        return try {
            val level = value?.toIntOrNull() ?: 50
            val brightnessLevel = (level * 255) / 100 // Convert 0-100 to 0-255

            Log.d(TAG, "🔆 Setting brightness to $level% (system value: $brightnessLevel)")

            // Check if we have WRITE_SETTINGS permission
            if (android.provider.Settings.System.canWrite(context)) {
                android.provider.Settings.System.putInt(
                    context.contentResolver,
                    android.provider.Settings.System.SCREEN_BRIGHTNESS,
                    brightnessLevel
                )

                // Also set brightness mode to manual
                android.provider.Settings.System.putInt(
                    context.contentResolver,
                    android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE,
                    android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
                )

                Log.d(TAG, "✅ Brightness set to $level%")
                true
            } else {
                Log.w(TAG, "⚠️ No WRITE_SETTINGS permission, opening display settings")
                openDisplaySettings()
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error setting brightness", e)
            openDisplaySettings()
            false
        }
    }

    /**
     * Adjust brightness up or down
     */
    private fun adjustBrightness(direction: String?): Boolean {
        return try {
            val currentBrightness = android.provider.Settings.System.getInt(
                context.contentResolver,
                android.provider.Settings.System.SCREEN_BRIGHTNESS,
                128
            )

            val currentPercent = (currentBrightness * 100) / 255
            val adjustment = when (direction?.lowercase()) {
                "up", "increase", "higher" -> 10
                "down", "decrease", "lower" -> -10
                else -> 10
            }

            val newPercent = (currentPercent + adjustment).coerceIn(0, 100)
            setBrightness(newPercent.toString())
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error adjusting brightness", e)
            openDisplaySettings()
            false
        }
    }

    /**
     * Smart WiFi toggle with multiple fallback methods
     */
    private fun smartToggleWifi(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart WiFi toggle: $state")

            val targetState = when (state?.lowercase()) {
                "on", "enable", "true" -> true
                "off", "disable", "false" -> false
                else -> !wifiManager.isWifiEnabled // Toggle current state
            }

            Log.d(TAG, "🎯 Target WiFi state: ${if (targetState) "ON" else "OFF"}")
            Log.d(TAG, "📊 Current WiFi state: ${if (wifiManager.isWifiEnabled) "ON" else "OFF"}")

            // Method 1: Device Admin approach (enhanced permissions)
            if (tryDeviceAdminWifiToggle(targetState)) {
                Log.d(TAG, "✅ WiFi toggled via Device Admin")
                // Wait a moment to verify the change
                serviceScope.launch {
                    delay(500)
                    val newState = wifiManager.isWifiEnabled
                    Log.d(TAG, "📊 WiFi state after Device Admin toggle: ${if (newState) "ON" else "OFF"}")
                }
                return true
            }

            // Method 2: Direct API (works on older Android versions)
            if (tryDirectWifiToggle(targetState)) {
                Log.d(TAG, "✅ WiFi toggled via direct API")
                // Wait a moment to verify the change
                serviceScope.launch {
                    delay(500)
                    val newState = wifiManager.isWifiEnabled
                    Log.d(TAG, "📊 WiFi state after direct toggle: ${if (newState) "ON" else "OFF"}")
                }
                return true
            }

            // Method 2: Reflection-based toggle (for system apps)
            if (tryReflectionWifiToggle(targetState)) {
                Log.d(TAG, "✅ WiFi toggled via reflection")
                // Wait a moment to verify the change
                serviceScope.launch {
                    delay(500)
                    val newState = wifiManager.isWifiEnabled
                    Log.d(TAG, "📊 WiFi state after reflection toggle: ${if (newState) "ON" else "OFF"}")
                }
                return true
            }

            // Method 3: Try broadcast intent approach
            if (tryBroadcastWifiToggle(targetState)) {
                Log.d(TAG, "✅ WiFi toggled via broadcast")
                return true
            }

            // Method 4: Check for restricted settings issue
            if (!Settings.System.canWrite(context)) {
                Log.w(TAG, "⚠️ RESTRICTED SETTINGS DETECTED - This is likely the issue!")
                Log.w(TAG, "📱 Opening Restricted Settings Guide...")
                openRestrictedSettingsGuide()
                return false
            }

            // Method 5: Settings intent with accessibility service
            if (tryAccessibilityWifiToggle(targetState)) {
                Log.d(TAG, "✅ WiFi toggled via accessibility service")
                return true
            }

            // Method 6: Fallback to settings page
            Log.d(TAG, "⚠️ Using fallback: Opening WiFi settings")
            openWifiSettingsWithInstructions(targetState)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in smart WiFi toggle", e)
            false
        }
    }

    /**
     * Try direct WiFi toggle (enhanced for all Android versions)
     */
    private fun tryDirectWifiToggle(enable: Boolean): Boolean {
        return try {
            // Method 1: Try the deprecated but still working method
            @Suppress("DEPRECATION")
            val result = wifiManager.setWifiEnabled(enable)
            if (result) {
                Log.d(TAG, "🎯 Direct WiFi toggle successful via setWifiEnabled")
                return true
            }

            // Method 2: Try using WifiManager reflection for newer Android versions
            val wifiManagerClass = wifiManager.javaClass
            val setWifiEnabledMethod = wifiManagerClass.getDeclaredMethod("setWifiEnabled", Boolean::class.java)
            setWifiEnabledMethod.isAccessible = true
            val reflectionResult = setWifiEnabledMethod.invoke(wifiManager, enable) as Boolean

            if (reflectionResult) {
                Log.d(TAG, "🎯 Direct WiFi toggle successful via reflection")
                return true
            }

            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Direct WiFi toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Try reflection-based WiFi toggle (for system-level access)
     */
    private fun tryReflectionWifiToggle(enable: Boolean): Boolean {
        return try {
            // Method 1: Try ConnectivityManager approach
            try {
                val connectivityManagerClass = connectivityManager.javaClass
                val setWifiEnabledMethod: Method = connectivityManagerClass.getDeclaredMethod("setWifiEnabled", Boolean::class.java)
                setWifiEnabledMethod.isAccessible = true
                val result = setWifiEnabledMethod.invoke(connectivityManager, enable) as Boolean

                if (result) {
                    Log.d(TAG, "🔧 Reflection WiFi toggle successful via ConnectivityManager")
                    return true
                }
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ ConnectivityManager reflection failed: ${e.message}")
            }

            // Method 2: Try Settings.Global approach (like airplane mode)
            try {
                val currentState = if (wifiManager.isWifiEnabled) 1 else 0
                val targetState = if (enable) 1 else 0

                if (currentState != targetState) {
                    // This might work on some devices
                    Settings.Global.putInt(context.contentResolver, "wifi_on", targetState)
                    Log.d(TAG, "🔧 WiFi toggle via Settings.Global")
                    return true
                }
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ Settings.Global WiFi toggle failed: ${e.message}")
            }

            // Method 3: Try system command approach
            try {
                val command = if (enable) "svc wifi enable" else "svc wifi disable"
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", command))
                val exitCode = process.waitFor()

                if (exitCode == 0) {
                    Log.d(TAG, "🔧 WiFi toggle via system command successful")
                    return true
                }
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ System command WiFi toggle failed: ${e.message}")
            }

            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Reflection WiFi toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Try broadcast intent WiFi toggle
     */
    private fun tryBroadcastWifiToggle(enable: Boolean): Boolean {
        return try {
            // Send WiFi state change broadcast
            val intent = Intent("android.net.wifi.WIFI_STATE_CHANGED").apply {
                putExtra("wifi_state", if (enable) 3 else 1) // WIFI_STATE_ENABLED = 3, WIFI_STATE_DISABLED = 1
                putExtra("previous_wifi_state", if (enable) 1 else 3)
            }
            context.sendBroadcast(intent)

            // Also try the action intent
            val actionIntent = Intent().apply {
                action = if (enable) "android.net.wifi.WIFI_STATE_CHANGED" else "android.net.wifi.WIFI_STATE_CHANGED"
                putExtra("wifi_state", if (enable) 3 else 1)
            }
            context.sendBroadcast(actionIntent)

            Log.d(TAG, "🔧 WiFi broadcast sent: ${if (enable) "ENABLE" else "DISABLE"}")

            // Check if it worked after a short delay
            serviceScope.launch {
                delay(1000)
                val currentState = wifiManager.isWifiEnabled
                if (currentState == enable) {
                    Log.d(TAG, "✅ Broadcast WiFi toggle successful")
                } else {
                    Log.d(TAG, "⚠️ Broadcast WiFi toggle may not have worked")
                }
            }

            true
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Broadcast WiFi toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Try accessibility service WiFi toggle
     */
    private fun tryAccessibilityWifiToggle(enable: Boolean): Boolean {
        return try {
            if (!ZaraAccessibilityService.isEnabled(context)) {
                Log.d(TAG, "⚠️ Accessibility service not enabled")
                return false
            }

            // Open WiFi settings
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            // Use coroutine to wait and then trigger accessibility action
            serviceScope.launch {
                delay(SMART_TOGGLE_DELAY)
                // Here we would communicate with ZaraAccessibilityService to find and click the WiFi toggle
                // For now, we'll implement this in the accessibility service enhancement
                Log.d(TAG, "🤖 Accessibility service should toggle WiFi now")
            }

            Log.d(TAG, "📱 Opened WiFi settings for accessibility toggle")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Accessibility WiFi toggle failed", e)
            false
        }
    }

    /**
     * Open WiFi settings with voice instructions
     */
    private fun openWifiSettingsWithInstructions(enable: Boolean): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = if (enable) "Please turn on WiFi in the settings" else "Please turn off WiFi in the settings"
            Log.d(TAG, "📱 $instruction")

            // Here you could also trigger TTS to speak the instruction
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening WiFi settings", e)
            false
        }
    }

    /**
     * Open WiFi settings
     */
    private fun openWifiSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening WiFi settings", e)
            false
        }
    }

    /**
     * Smart Bluetooth toggle with multiple fallback methods
     */
    private fun smartToggleBluetooth(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Bluetooth toggle: $state")

            if (bluetoothAdapter == null) {
                Log.e(TAG, "❌ Bluetooth not supported on this device")
                return false
            }

            val targetState = when (state?.lowercase()) {
                "on", "enable", "true" -> true
                "off", "disable", "false" -> false
                else -> !bluetoothAdapter!!.isEnabled // Toggle current state
            }

            Log.d(TAG, "🎯 Target Bluetooth state: ${if (targetState) "ON" else "OFF"}")
            Log.d(TAG, "📊 Current Bluetooth state: ${if (bluetoothAdapter!!.isEnabled) "ON" else "OFF"}")

            // Method 1: Direct API toggle
            if (tryDirectBluetoothToggle(targetState)) {
                Log.d(TAG, "✅ Bluetooth toggled via direct API")
                return true
            }

            // Method 2: Reflection-based toggle
            if (tryReflectionBluetoothToggle(targetState)) {
                Log.d(TAG, "✅ Bluetooth toggled via reflection")
                return true
            }

            // Method 3: Settings intent with accessibility service
            if (tryAccessibilityBluetoothToggle(targetState)) {
                Log.d(TAG, "✅ Bluetooth toggled via accessibility service")
                return true
            }

            // Method 4: Fallback to settings page
            Log.d(TAG, "⚠️ Using fallback: Opening Bluetooth settings")
            openBluetoothSettingsWithInstructions(targetState)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in smart Bluetooth toggle", e)
            false
        }
    }

    /**
     * Try direct Bluetooth toggle
     */
    private fun tryDirectBluetoothToggle(enable: Boolean): Boolean {
        return try {
            if (bluetoothAdapter == null) return false

            val result = if (enable) {
                if (!bluetoothAdapter!!.isEnabled) {
                    bluetoothAdapter!!.enable()
                } else true
            } else {
                if (bluetoothAdapter!!.isEnabled) {
                    bluetoothAdapter!!.disable()
                } else true
            }

            if (result) {
                Log.d(TAG, "🎯 Direct Bluetooth toggle initiated")
                return true
            }
            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Direct Bluetooth toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Try reflection-based Bluetooth toggle
     */
    private fun tryReflectionBluetoothToggle(enable: Boolean): Boolean {
        return try {
            if (bluetoothAdapter == null) return false

            val bluetoothClass = bluetoothAdapter!!.javaClass
            val method = if (enable) {
                bluetoothClass.getDeclaredMethod("enable")
            } else {
                bluetoothClass.getDeclaredMethod("disable")
            }

            method.isAccessible = true
            val result = method.invoke(bluetoothAdapter) as Boolean

            if (result) {
                Log.d(TAG, "🔧 Reflection Bluetooth toggle successful")
                return true
            }
            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Reflection Bluetooth toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Try accessibility service Bluetooth toggle
     */
    private fun tryAccessibilityBluetoothToggle(enable: Boolean): Boolean {
        return try {
            if (!ZaraAccessibilityService.isEnabled(context)) {
                Log.d(TAG, "⚠️ Accessibility service not enabled")
                return false
            }

            // Open Bluetooth settings
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            // Use coroutine to wait and then trigger accessibility action
            serviceScope.launch {
                delay(SMART_TOGGLE_DELAY)
                // Here we would communicate with ZaraAccessibilityService to find and click the Bluetooth toggle
                Log.d(TAG, "🤖 Accessibility service should toggle Bluetooth now")
            }

            Log.d(TAG, "📱 Opened Bluetooth settings for accessibility toggle")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Accessibility Bluetooth toggle failed", e)
            false
        }
    }

    /**
     * Open Bluetooth settings with voice instructions
     */
    private fun openBluetoothSettingsWithInstructions(enable: Boolean): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = if (enable) "Please turn on Bluetooth in the settings" else "Please turn off Bluetooth in the settings"
            Log.d(TAG, "📱 $instruction")

            // Here you could also trigger TTS to speak the instruction
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening Bluetooth settings", e)
            false
        }
    }

    /**
     * Open Bluetooth settings
     */
    private fun openBluetoothSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Bluetooth settings", e)
            false
        }
    }

    /**
     * Open display settings
     */
    private fun openDisplaySettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening display settings", e)
            false
        }
    }

    /**
     * Open sound settings
     */
    private fun openSoundSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_SOUND_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening sound settings", e)
            false
        }
    }

    /**
     * Open security settings
     */
    private fun openSecuritySettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_SECURITY_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening security settings", e)
            false
        }
    }

    /**
     * Open accessibility settings
     */
    private fun openAccessibilitySettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening accessibility settings", e)
            false
        }
    }

    /**
     * Open general settings
     */
    private fun openSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error opening settings", e)
            false
        }
    }

    /**
     * Open an app by package name or app name
     */
    private fun openApp(appIdentifier: String): Boolean {
        return try {
            // Try to open by package name first
            val intent = context.packageManager.getLaunchIntentForPackage(appIdentifier)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true
            }
            
            // If not found, try to find by app name
            return openAppByName(appIdentifier)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening app: $appIdentifier", e)
            false
        }
    }

    /**
     * Open app by name
     */
    private fun openAppByName(appName: String): Boolean {
        return try {
            Log.d(TAG, "🔍 Searching for app: '$appName'")

            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }

            val apps = context.packageManager.queryIntentActivities(intent, 0)
            Log.d(TAG, "📱 Found ${apps.size} installed apps")

            // Smart app matching with fuzzy logic for ANY app
            val searchTerm = appName.lowercase().trim()
            Log.d(TAG, "🔍 Smart search for: '$searchTerm'")

            // Create scored matches for all apps
            val scoredApps = apps.mapNotNull { resolveInfo ->
                val appLabel = context.packageManager.getApplicationLabel(resolveInfo.activityInfo.applicationInfo)
                    .toString().lowercase().trim()
                val packageName = resolveInfo.activityInfo.packageName.lowercase()

                val score = calculateAppMatchScore(searchTerm, appLabel, packageName)
                if (score > 0) {
                    Log.d(TAG, "📊 App: '$appLabel' - Score: $score")
                    Pair(resolveInfo, score)
                } else null
            }.sortedByDescending { it.second }

            val targetApp = scoredApps.firstOrNull()?.first

            if (targetApp != null) {
                val appLabel = context.packageManager.getApplicationLabel(targetApp.activityInfo.applicationInfo)
                val score = scoredApps.first().second
                Log.d(TAG, "🎯 Best match: '$appLabel' (Score: $score)")
            }

            if (targetApp != null) {
                val appLabel = context.packageManager.getApplicationLabel(targetApp.activityInfo.applicationInfo)
                Log.d(TAG, "🚀 Opening app: $appLabel")

                val launchIntent = Intent().apply {
                    component = android.content.ComponentName(
                        targetApp.activityInfo.packageName,
                        targetApp.activityInfo.name
                    )
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(launchIntent)
                Log.d(TAG, "✅ App launched successfully")
                true
            } else {
                Log.w(TAG, "❌ App not found: $appName")

                // Get all available apps for better suggestions
                val availableApps = apps.map {
                    context.packageManager.getApplicationLabel(it.activityInfo.applicationInfo).toString()
                }
                Log.d(TAG, "📱 Available apps (first 10): ${availableApps.take(10).joinToString(", ")}")

                // Smart suggestions based on what user might have meant
                val smartSuggestions = getSmartAppSuggestions(appName, availableApps)
                if (smartSuggestions.isNotEmpty()) {
                    Log.d(TAG, "💡 Did you mean: ${smartSuggestions.joinToString(", ")}?")

                    // Auto-open the best match if confidence is high
                    val bestMatch = smartSuggestions.first()
                    val bestMatchApp = apps.find {
                        context.packageManager.getApplicationLabel(it.activityInfo.applicationInfo)
                            .toString().equals(bestMatch, ignoreCase = true)
                    }

                    if (bestMatchApp != null) {
                        Log.d(TAG, "🎯 Auto-opening best match: $bestMatch")
                        val launchIntent = Intent().apply {
                            component = android.content.ComponentName(
                                bestMatchApp.activityInfo.packageName,
                                bestMatchApp.activityInfo.name
                            )
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        context.startActivity(launchIntent)
                        Log.d(TAG, "✅ App launched successfully: $bestMatch")
                        return true
                    }
                }

                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening app by name: $appName", e)
            false
        }
    }

    /**
     * Calculate smart match score for any app
     */
    private fun calculateAppMatchScore(searchTerm: String, appLabel: String, packageName: String): Int {
        var score = 0

        // Exact match (highest priority)
        if (appLabel == searchTerm) {
            score += 100
        }

        // Starts with search term
        if (appLabel.startsWith(searchTerm)) {
            score += 80
        }

        // Contains search term
        if (appLabel.contains(searchTerm)) {
            score += 60
        }

        // Word boundary matches (e.g., "whats" matches "WhatsApp")
        val appWords = appLabel.split(" ", "-", "_", ".")
        val searchWords = searchTerm.split(" ", "-", "_", ".")

        for (searchWord in searchWords) {
            for (appWord in appWords) {
                if (appWord.startsWith(searchWord) && searchWord.length >= 3) {
                    score += 40
                }
                if (appWord.contains(searchWord) && searchWord.length >= 3) {
                    score += 20
                }
            }
        }

        // Package name matching
        if (packageName.contains(searchTerm)) {
            score += 30
        }

        // Common abbreviations and variations
        val commonMappings = mapOf(
            "whatsapp" to listOf("whats", "wa"),
            "spotify" to listOf("music", "songs"),
            "facebook" to listOf("fb"),
            "instagram" to listOf("insta", "ig"),
            "telegram" to listOf("tg"),
            "calculator" to listOf("calc"),
            "calendar" to listOf("cal"),
            "camera" to listOf("cam"),
            "settings" to listOf("setting", "config"),
            "chrome" to listOf("browser"),
            "gmail" to listOf("mail", "email"),
            "maps" to listOf("navigation", "nav"),
            "gallery" to listOf("photos", "images", "pics"),
            "messages" to listOf("sms", "text", "msg"),
            "phone" to listOf("dialer", "call"),
            "contacts" to listOf("phonebook")
        )

        // Check if search term matches any known abbreviations
        for ((fullName, abbreviations) in commonMappings) {
            if (appLabel.contains(fullName)) {
                if (abbreviations.contains(searchTerm)) {
                    score += 70
                }
            }
        }

        // Check reverse - if search is full name and app contains abbreviation
        if (commonMappings[searchTerm]?.any { appLabel.contains(it) } == true) {
            score += 50
        }

        // Fuzzy matching for typos (simple Levenshtein-like)
        if (score == 0 && searchTerm.length >= 4) {
            val similarity = calculateSimilarity(searchTerm, appLabel)
            if (similarity > 0.7) {
                score += (similarity * 30).toInt()
            }
        }

        return score
    }

    /**
     * Calculate string similarity (simple version)
     */
    private fun calculateSimilarity(s1: String, s2: String): Double {
        val longer = if (s1.length > s2.length) s1 else s2
        val shorter = if (s1.length > s2.length) s2 else s1

        if (longer.isEmpty()) return 1.0

        val editDistance = calculateEditDistance(longer, shorter)
        return (longer.length - editDistance) / longer.length.toDouble()
    }

    /**
     * Calculate edit distance between two strings
     */
    private fun calculateEditDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j

        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[s1.length][s2.length]
    }

    /**
     * Get smart app suggestions based on user input and available apps
     */
    private fun getSmartAppSuggestions(userInput: String, availableApps: List<String>): List<String> {
        val input = userInput.lowercase().trim()

        // Direct mapping for common requests to actual app names
        val commonMappings = mapOf(
            "whatsapp" to listOf("WhatsApp", "WhatsApp Business", "WhatsApp Messenger"),
            "chrome" to listOf("Chrome", "Google Chrome", "Chrome Browser"),
            "browser" to listOf("Chrome", "Firefox", "Samsung Internet", "Browser"),
            "camera" to listOf("Camera", "Google Camera", "Samsung Camera"),
            "gallery" to listOf("Gallery", "Photos", "Google Photos"),
            "settings" to listOf("Settings", "System Settings"),
            "store" to listOf("Google Play Store", "Play Store", "App Store"),
            "spotify" to listOf("Spotify", "Spotify Music"),
            "gmail" to listOf("Gmail", "Google Mail", "Mail"),
            "maps" to listOf("Maps", "Google Maps", "Navigation"),
            "music" to listOf("Spotify", "Music", "Samsung Music"),
            "calculator" to listOf("Calculator", "Google Calculator"),
            "calendar" to listOf("Calendar", "Google Calendar"),
            "contacts" to listOf("Contacts", "Google Contacts", "Phone"),
            "phone" to listOf("Phone", "Dialer", "Call"),
            "messages" to listOf("Messages", "SMS", "Google Messages"),
            "facebook" to listOf("Facebook", "FB"),
            "instagram" to listOf("Instagram", "IG"),
            "twitter" to listOf("Twitter", "X"),
            "telegram" to listOf("Telegram", "Telegram X"),
            "spotify" to listOf("Spotify", "Spotify Music"),
            "netflix" to listOf("Netflix")
        )

        // First try direct mapping
        val directMatches = commonMappings[input]?.filter { suggestion ->
            availableApps.any { it.equals(suggestion, ignoreCase = true) }
        } ?: emptyList()

        if (directMatches.isNotEmpty()) {
            return directMatches
        }

        // Then try fuzzy matching with available apps
        val fuzzyMatches = availableApps.mapNotNull { appName ->
            val score = calculateAppMatchScore(input, appName.lowercase(), "")
            if (score > 20) Pair(appName, score) else null
        }.sortedByDescending { it.second }.take(3).map { it.first }

        return fuzzyMatches
    }

    /**
     * Close current app (requires accessibility service)
     */
    private fun closeApp(): Boolean {
        return performGlobalAction("back")
    }

    /**
     * Switch to app (requires accessibility service)
     */
    private fun switchToApp(appIdentifier: String): Boolean {
        // First open recent apps, then the accessibility service would need to handle selection
        return performGlobalAction("recents")
    }

    /**
     * Perform global action through accessibility service
     * This is a placeholder - actual implementation would need to communicate with ZaraAccessibilityService
     */
    private fun performGlobalAction(action: String): Boolean {
        // In a real implementation, this would send a broadcast or use a service connection
        // to communicate with ZaraAccessibilityService
        Log.d(TAG, "Global action requested: $action")
        
        // For now, we'll return true to indicate the request was received
        // The actual implementation would depend on how you want to communicate with the accessibility service
        return true
    }

    /**
     * Smart Airplane Mode toggle
     */
    private fun smartToggleAirplaneMode(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Airplane Mode toggle: $state")

            val targetState = when (state?.lowercase()) {
                "on", "enable", "true" -> 1
                "off", "disable", "false" -> 0
                else -> {
                    val currentState = Settings.Global.getInt(context.contentResolver, Settings.Global.AIRPLANE_MODE_ON, 0)
                    if (currentState == 0) 1 else 0
                }
            }

            // Try to set airplane mode directly
            if (tryDirectAirplaneModeToggle(targetState)) {
                Log.d(TAG, "✅ Airplane mode toggled directly")
                return true
            }

            // Fallback to settings
            openAirplaneModeSettings()

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling airplane mode", e)
            false
        }
    }

    private fun tryDirectAirplaneModeToggle(state: Int): Boolean {
        return try {
            // Method 1: Direct Settings.Global approach
            Settings.Global.putInt(context.contentResolver, Settings.Global.AIRPLANE_MODE_ON, state)

            // Broadcast the change
            val intent = Intent(Intent.ACTION_AIRPLANE_MODE_CHANGED).apply {
                putExtra("state", state == 1)
            }
            context.sendBroadcast(intent)

            Log.d(TAG, "🎯 Airplane mode set to: ${if (state == 1) "ON" else "OFF"}")

            // Verify the change worked
            serviceScope.launch {
                delay(500)
                val currentState = Settings.Global.getInt(context.contentResolver, Settings.Global.AIRPLANE_MODE_ON, 0)
                Log.d(TAG, "📊 Airplane mode state after toggle: ${if (currentState == 1) "ON" else "OFF"}")
            }

            true
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Direct airplane mode toggle failed: ${e.message}")
            false
        }
    }

    private fun openAirplaneModeSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_AIRPLANE_MODE_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            Log.d(TAG, "📱 Opened airplane mode settings")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening airplane mode settings", e)
            false
        }
    }

    /**
     * Smart Mobile Data toggle
     */
    private fun smartToggleMobileData(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Mobile Data toggle: $state")

            val targetState = when (state?.lowercase()) {
                "on", "enable", "true" -> true
                "off", "disable", "false" -> false
                else -> !isMobileDataEnabled()
            }

            // Try reflection-based toggle
            if (tryReflectionMobileDataToggle(targetState)) {
                Log.d(TAG, "✅ Mobile data toggled via reflection")
                return true
            }

            // Fallback to settings
            openMobileDataSettings()

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling mobile data", e)
            false
        }
    }

    private fun isMobileDataEnabled(): Boolean {
        return try {
            val connectivityManagerClass = connectivityManager.javaClass
            val method = connectivityManagerClass.getDeclaredMethod("getMobileDataEnabled")
            method.isAccessible = true
            method.invoke(connectivityManager) as Boolean
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Could not check mobile data state: ${e.message}")
            false
        }
    }

    private fun tryReflectionMobileDataToggle(enable: Boolean): Boolean {
        return try {
            // Method 1: ConnectivityManager reflection
            try {
                val connectivityManagerClass = connectivityManager.javaClass
                val method = connectivityManagerClass.getDeclaredMethod("setMobileDataEnabled", Boolean::class.java)
                method.isAccessible = true
                method.invoke(connectivityManager, enable)

                Log.d(TAG, "🔧 Mobile data set via reflection: ${if (enable) "ON" else "OFF"}")

                // Verify the change
                serviceScope.launch {
                    delay(500)
                    val currentState = isMobileDataEnabled()
                    Log.d(TAG, "📊 Mobile data state after toggle: ${if (currentState) "ON" else "OFF"}")
                }

                return true
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ ConnectivityManager reflection failed: ${e.message}")
            }

            // Method 2: TelephonyManager approach
            try {
                val telephonyManagerClass = Class.forName("android.telephony.TelephonyManager")
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE)
                val setDataEnabledMethod = telephonyManagerClass.getDeclaredMethod("setDataEnabled", Boolean::class.java)
                setDataEnabledMethod.isAccessible = true
                setDataEnabledMethod.invoke(telephonyManager, enable)

                Log.d(TAG, "🔧 Mobile data set via TelephonyManager: ${if (enable) "ON" else "OFF"}")
                return true
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ TelephonyManager approach failed: ${e.message}")
            }

            // Method 3: System command approach
            try {
                val command = if (enable) "svc data enable" else "svc data disable"
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", command))
                val exitCode = process.waitFor()

                if (exitCode == 0) {
                    Log.d(TAG, "🔧 Mobile data toggle via system command successful")
                    return true
                }
            } catch (e: Exception) {
                Log.d(TAG, "⚠️ System command mobile data toggle failed: ${e.message}")
            }

            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Reflection mobile data toggle failed: ${e.message}")
            false
        }
    }

    private fun openMobileDataSettings(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            Log.d(TAG, "📱 Opened mobile data settings")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening mobile data settings", e)
            false
        }
    }

    /**
     * Smart Hotspot toggle
     */
    private fun smartToggleHotspot(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Hotspot toggle: $state")

            // Hotspot control requires system permissions
            // For now, open settings
            val intent = Intent(Settings.ACTION_WIRELESS_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = when (state?.lowercase()) {
                "on", "enable", "true" -> "Please enable Mobile Hotspot in settings"
                "off", "disable", "false" -> "Please disable Mobile Hotspot in settings"
                else -> "Please toggle Mobile Hotspot in settings"
            }
            Log.d(TAG, "📱 $instruction")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling hotspot", e)
            false
        }
    }

    /**
     * Smart NFC toggle
     */
    private fun smartToggleNFC(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart NFC toggle: $state")

            val intent = Intent(Settings.ACTION_NFC_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = when (state?.lowercase()) {
                "on", "enable", "true" -> "Please enable NFC in settings"
                "off", "disable", "false" -> "Please disable NFC in settings"
                else -> "Please toggle NFC in settings"
            }
            Log.d(TAG, "📱 $instruction")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling NFC", e)
            false
        }
    }

    /**
     * Smart Location toggle
     */
    private fun smartToggleLocation(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Location toggle: $state")

            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = when (state?.lowercase()) {
                "on", "enable", "true" -> "Please enable Location services in settings"
                "off", "disable", "false" -> "Please disable Location services in settings"
                else -> "Please toggle Location services in settings"
            }
            Log.d(TAG, "📱 $instruction")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling location", e)
            false
        }
    }

    /**
     * Smart Auto Rotate toggle
     */
    private fun smartToggleAutoRotate(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Auto Rotate toggle: $state")

            val targetState = when (state?.lowercase()) {
                "on", "enable", "true" -> 1
                "off", "disable", "false" -> 0
                else -> {
                    val currentState = Settings.System.getInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION, 0)
                    if (currentState == 0) 1 else 0
                }
            }

            // Direct toggle without opening settings
            Settings.System.putInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION, targetState)
            Log.d(TAG, "🎯 Auto rotate set directly to: ${if (targetState == 1) "ON" else "OFF"}")

            // Verify the change
            serviceScope.launch {
                delay(300)
                val currentState = Settings.System.getInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION, 0)
                Log.d(TAG, "📊 Auto rotate state after toggle: ${if (currentState == 1) "ON" else "OFF"}")
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling auto rotate", e)
            false
        }
    }

    /**
     * Smart Do Not Disturb toggle
     */
    private fun smartToggleDoNotDisturb(state: String?): Boolean {
        return try {
            Log.d(TAG, "🧠 Smart Do Not Disturb toggle: $state")

            val intent = Intent().apply {
                action = "android.settings.ZEN_MODE_SETTINGS"
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            val instruction = when (state?.lowercase()) {
                "on", "enable", "true" -> "Please enable Do Not Disturb in settings"
                "off", "disable", "false" -> "Please disable Do Not Disturb in settings"
                else -> "Please toggle Do Not Disturb in settings"
            }
            Log.d(TAG, "📱 $instruction")
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error toggling do not disturb", e)
            false
        }
    }

    /**
     * Check if device admin is enabled
     */
    fun isDeviceAdminEnabled(): Boolean {
        return devicePolicyManager.isAdminActive(deviceAdminComponent)
    }

    /**
     * Request device admin permissions
     */
    fun requestDeviceAdminPermissions() {
        try {
            if (!isDeviceAdminEnabled()) {
                Log.d(TAG, "📱 Requesting Device Admin permissions for enhanced system control...")
                val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN).apply {
                    putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, deviceAdminComponent)
                    putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION,
                        "Zara needs Device Admin permissions to control WiFi, Bluetooth, and other system settings via voice commands without opening settings.")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } else {
                Log.d(TAG, "✅ Device Admin already enabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error requesting Device Admin permissions", e)
        }
    }

    /**
     * Request restricted settings permissions (for Android 13+)
     */
    fun requestRestrictedSettingsPermissions() {
        try {
            if (!Settings.System.canWrite(context)) {
                Log.d(TAG, "📱 Requesting Restricted Settings permissions...")
                val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS).apply {
                    data = android.net.Uri.parse("package:${context.packageName}")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } else {
                Log.d(TAG, "✅ Restricted Settings already enabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error requesting Restricted Settings permissions", e)
        }
    }

    /**
     * Open app info settings for manual restricted settings enable
     */
    fun openAppInfoForRestrictedSettings() {
        try {
            Log.d(TAG, "📱 Opening App Info for Restricted Settings...")
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = android.net.Uri.parse("package:${context.packageName}")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening App Info", e)
        }
    }

    /**
     * Open the Restricted Settings Guide Activity
     */
    private fun openRestrictedSettingsGuide() {
        try {
            Log.d(TAG, "📱 Opening Restricted Settings Guide...")
            val intent = Intent(context, RestrictedSettingsGuideActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error opening Restricted Settings Guide", e)
            // Fallback to direct app info
            openAppInfoForRestrictedSettings()
        }
    }

    /**
     * Enhanced WiFi toggle with Device Admin support
     */
    private fun tryDeviceAdminWifiToggle(enable: Boolean): Boolean {
        return try {
            if (isDeviceAdminEnabled()) {
                Log.d(TAG, "🔧 Attempting WiFi toggle via Device Admin...")

                // Device Admin approach - this might work on some devices
                val result = if (enable) {
                    // Try to enable WiFi using device admin capabilities
                    wifiManager.setWifiEnabled(enable)
                } else {
                    // Try to disable WiFi using device admin capabilities
                    wifiManager.setWifiEnabled(enable)
                }

                if (result) {
                    Log.d(TAG, "✅ Device Admin WiFi toggle successful")
                    return true
                }
            } else {
                Log.d(TAG, "⚠️ Device Admin not enabled, requesting permissions...")
                requestDeviceAdminPermissions()
            }
            false
        } catch (e: Exception) {
            Log.d(TAG, "⚠️ Device Admin WiFi toggle failed: ${e.message}")
            false
        }
    }

    /**
     * Check if accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        return ZaraAccessibilityService.isEnabled(context)
    }

    // ==================== MUSIC CONTROL METHODS ====================

    /**
     * Play music with optional song/artist parameters (handled by LocalCommandProcessor)
     */
    private fun playMusic(parameters: Map<String, String>): Boolean {
        return try {
            Log.d(TAG, "🎵 Music control handled by LocalCommandProcessor")
            // Music logic is now handled in LocalCommandProcessor
            // This method just confirms the action was received
            true
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in music control", e)
            false
        }
    }







    /**
     * Play general music (any available music app)
     */
    private fun playGeneralMusic(): Boolean {
        return try {
            Log.d(TAG, "🎵 Playing general music")

            // Method 1: Send media play key event
            if (sendMediaKeyEvent(KeyEvent.KEYCODE_MEDIA_PLAY)) {
                Log.d(TAG, "✅ Sent media play key event")
                return true
            }

            // Method 2: Try Spotify
            val spotifyIntent = context.packageManager.getLaunchIntentForPackage("com.spotify.music")
            if (spotifyIntent != null) {
                spotifyIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(spotifyIntent)
                Log.d(TAG, "✅ Opened Spotify")
                return true
            }

            // Method 3: Try to open default music app
            val intent = Intent(MediaStore.INTENT_ACTION_MEDIA_PLAY_FROM_SEARCH).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                Log.d(TAG, "✅ Started default music app")
                return true
            }

            Log.w(TAG, "⚠️ No music app available")
            false

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error playing general music", e)
            false
        }
    }

    /**
     * Pause currently playing music
     */
    private fun pauseMusic(): Boolean {
        return try {
            Log.d(TAG, "⏸️ Pausing music")
            sendMediaKeyEvent(KeyEvent.KEYCODE_MEDIA_PAUSE)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error pausing music", e)
            false
        }
    }

    /**
     * Stop currently playing music
     */
    private fun stopMusic(): Boolean {
        return try {
            Log.d(TAG, "⏹️ Stopping music")
            sendMediaKeyEvent(KeyEvent.KEYCODE_MEDIA_STOP)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error stopping music", e)
            false
        }
    }

    /**
     * Skip to next track
     */
    private fun nextTrack(): Boolean {
        return try {
            Log.d(TAG, "⏭️ Next track")
            sendMediaKeyEvent(KeyEvent.KEYCODE_MEDIA_NEXT)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error skipping to next track", e)
            false
        }
    }

    /**
     * Go to previous track
     */
    private fun previousTrack(): Boolean {
        return try {
            Log.d(TAG, "⏮️ Previous track")
            sendMediaKeyEvent(KeyEvent.KEYCODE_MEDIA_PREVIOUS)
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error going to previous track", e)
            false
        }
    }

    /**
     * Send media key event to control music playback
     */
    private fun sendMediaKeyEvent(keyCode: Int): Boolean {
        return try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            // Create key events
            val downEvent = KeyEvent(KeyEvent.ACTION_DOWN, keyCode)
            val upEvent = KeyEvent(KeyEvent.ACTION_UP, keyCode)

            // Send the key events
            audioManager.dispatchMediaKeyEvent(downEvent)
            audioManager.dispatchMediaKeyEvent(upEvent)

            Log.d(TAG, "✅ Sent media key event: $keyCode")
            true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error sending media key event: $keyCode", e)
            false
        }
    }
}
