package com.zara.assistant

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
// Removed VoskInitializer - using clean Android STT instead
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Zara Application class
 * Initializes Hilt dependency injection and sets up notification channels
 */
@HiltAndroidApp
class ZaraApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()

        // Initialize notification channels
        createNotificationChannels()

        // Initialize WorkManager with Hilt
        WorkManager.initialize(this, workManagerConfiguration)

        // Initialize Vosk model in background
        initializeVoskModel()
    }

    private fun initializeVoskModel() {
        // Vosk removed - using clean Android STT service instead
        // STT initialization happens in VoiceProcessingService
        android.util.Log.d("ZaraApplication", "Using clean Android STT service - no model initialization needed")
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Wake Word Service Channel
            val wakeWordChannel = NotificationChannel(
                WAKE_WORD_CHANNEL_ID,
                "Wake Word Detection",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Ongoing notification for wake word detection service"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            // Voice Processing Channel
            val voiceProcessingChannel = NotificationChannel(
                VOICE_PROCESSING_CHANNEL_ID,
                "Voice Processing",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for voice command processing"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            // AI Response Channel
            val aiResponseChannel = NotificationChannel(
                AI_RESPONSE_CHANNEL_ID,
                "AI Responses",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for AI responses and alerts"
                setShowBadge(true)
                enableLights(true)
                enableVibration(true)
            }
            
            // System Control Channel
            val systemControlChannel = NotificationChannel(
                SYSTEM_CONTROL_CHANNEL_ID,
                "System Control",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for system control actions"
                setShowBadge(true)
                enableLights(true)
                enableVibration(true)
            }
            
            // Error Channel
            val errorChannel = NotificationChannel(
                ERROR_CHANNEL_ID,
                "Errors",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Error notifications and alerts"
                setShowBadge(true)
                enableLights(true)
                enableVibration(true)
            }
            
            notificationManager.createNotificationChannels(
                listOf(
                    wakeWordChannel,
                    voiceProcessingChannel,
                    aiResponseChannel,
                    systemControlChannel,
                    errorChannel
                )
            )
        }
    }

    companion object {
        const val WAKE_WORD_CHANNEL_ID = "wake_word_channel"
        const val VOICE_PROCESSING_CHANNEL_ID = "voice_processing_channel"
        const val AI_RESPONSE_CHANNEL_ID = "ai_response_channel"
        const val SYSTEM_CONTROL_CHANNEL_ID = "system_control_channel"
        const val ERROR_CHANNEL_ID = "error_channel"
        
        // Notification IDs
        const val WAKE_WORD_NOTIFICATION_ID = 1001
        const val VOICE_PROCESSING_NOTIFICATION_ID = 1002
        const val AI_RESPONSE_NOTIFICATION_ID = 1003
        const val SYSTEM_CONTROL_NOTIFICATION_ID = 1004
        const val ERROR_NOTIFICATION_ID = 1005
    }
}
