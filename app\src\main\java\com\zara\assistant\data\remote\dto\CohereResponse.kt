package com.zara.assistant.data.remote.dto

/**
 * Cohere API response models
 */
data class CohereResponse(
    val id: String,
    val generations: List<CohereGeneration>,
    val prompt: String,
    val meta: CohereMeta?
)

data class CohereGeneration(
    val id: String,
    val text: String,
    val likelihood: Float?,
    val token_likelihoods: List<CohereTokenLikelihood>?
)

data class CohereTokenLikelihood(
    val token: String,
    val likelihood: Float
)

data class CohereMeta(
    val api_version: CohereApiVersion?,
    val billed_units: CohereBilledUnits?
)

data class CohereApiVersion(
    val version: String
)

data class CohereBilledUnits(
    val input_tokens: Int?,
    val output_tokens: Int?
)

data class CohereClassifyResponse(
    val id: String,
    val classifications: List<CohereClassification>,
    val meta: CohereMeta?
)

data class CohereClassification(
    val id: String,
    val input: String,
    val prediction: String,
    val confidence: Float,
    val labels: Map<String, CohereLabel>
)

data class CohereLabel(
    val confidence: Float
)

data class CohereSummarizeResponse(
    val id: String,
    val summary: String,
    val meta: CohereMeta?
)

/**
 * Error response model
 */
data class CohereErrorResponse(
    val message: String,
    val code: String?
)
