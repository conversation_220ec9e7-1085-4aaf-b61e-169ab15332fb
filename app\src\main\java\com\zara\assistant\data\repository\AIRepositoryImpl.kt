package com.zara.assistant.data.repository

import android.util.Log
import com.zara.assistant.core.Constants
import com.zara.assistant.data.remote.api.CohereApiService
import com.zara.assistant.data.remote.dto.CohereGenerateRequest
import com.zara.assistant.data.remote.api.PerplexityApiService
import com.zara.assistant.data.remote.dto.PerplexityMessage
import com.zara.assistant.data.remote.dto.PerplexityChatRequest
import com.zara.assistant.domain.model.AIResponse
import com.zara.assistant.domain.model.AISource
import com.zara.assistant.domain.model.ActionType
import com.zara.assistant.domain.model.CommandType
import com.zara.assistant.domain.model.SystemAction
import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.repository.AIRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of AIRepository
 */
@Singleton
class AIRepositoryImpl @Inject constructor(
    private val cohereApiService: CohereApiService,
    private val perplexityApiService: PerplexityApiService
) : AIRepository {

    companion object {
        private const val TAG = "AIRepositoryImpl"
    }

    private val responseCache = mutableMapOf<String, AIResponse>()
    private val _preferredAISource = MutableStateFlow(AISource.COHERE)

    override suspend fun generateResponse(
        command: VoiceCommand,
        conversationHistory: List<String>,
        personality: Constants.AIPersonality,
        responseStyle: Constants.AIResponseStyle
    ): Result<AIResponse> {
        return try {
            // Check cache first
            val cacheKey = generateCacheKey(command.text, personality, responseStyle)
            responseCache[cacheKey]?.let { cachedResponse ->
                Log.d(TAG, "Returning cached response")
                return Result.success(cachedResponse)
            }

            // Build context prompt
            val prompt = buildContextPrompt(command.text, conversationHistory, personality, responseStyle)
            
            // Try primary AI source first
            val response = when (_preferredAISource.value) {
                AISource.COHERE -> generateCohereResponse(prompt)
                AISource.PERPLEXITY -> searchPerplexity(command.text)
                else -> generateCohereResponse(prompt)
            }.getOrElse { error ->
                Log.w(TAG, "Primary AI source failed, trying fallback", error)
                // Try fallback source
                when (_preferredAISource.value) {
                    AISource.COHERE -> searchPerplexity(command.text)
                    AISource.PERPLEXITY -> generateCohereResponse(prompt)
                    else -> generateCohereResponse(prompt)
                }.getOrElse { fallbackError ->
                    Log.e(TAG, "Both AI sources failed", fallbackError)
                    return Result.failure(fallbackError)
                }
            }

            // Post-process response for voice output
            val processedResponse = processResponseForVoice(response, responseStyle)

            val aiResponse = AIResponse(
                id = UUID.randomUUID().toString(),
                text = processedResponse,
                timestamp = Date(),
                commandId = command.id,
                responseTime = System.currentTimeMillis() - command.timestamp.time,
                source = _preferredAISource.value,
                confidence = command.confidence,
                actions = extractSystemActions(processedResponse),
                metadata = mapOf(
                    "personality" to personality.name,
                    "response_style" to responseStyle.name,
                    "model_used" to _preferredAISource.value.name
                )
            )

            // Cache the response
            cacheResponse(cacheKey, aiResponse)
            
            Result.success(aiResponse)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating AI response", e)
            Result.failure(e)
        }
    }

    override suspend fun processCommand(command: VoiceCommand): Result<AIResponse> {
        return try {
            // Classify command type first
            val commandType = classifyCommand(command.text).getOrElse { CommandType.CONVERSATION }
            
            val updatedCommand = command.copy(commandType = commandType)
            
            // Generate appropriate response based on command type
            when (commandType) {
                CommandType.SYSTEM_CONTROL -> handleSystemCommand(updatedCommand)
                CommandType.INFORMATION -> handleInformationQuery(updatedCommand)
                CommandType.COMMUNICATION -> handleCommunicationCommand(updatedCommand)
                CommandType.ENTERTAINMENT -> handleEntertainmentCommand(updatedCommand)
                else -> generateResponse(updatedCommand)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing command", e)
            Result.failure(e)
        }
    }

    override suspend fun classifyCommand(text: String): Result<CommandType> {
        return try {
            val lowerText = text.lowercase()
            
            val commandType = when {
                containsSystemKeywords(lowerText) -> CommandType.SYSTEM_CONTROL
                containsInformationKeywords(lowerText) -> CommandType.INFORMATION
                containsCommunicationKeywords(lowerText) -> CommandType.COMMUNICATION
                containsEntertainmentKeywords(lowerText) -> CommandType.ENTERTAINMENT
                else -> CommandType.CONVERSATION
            }
            
            Result.success(commandType)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying command", e)
            Result.success(CommandType.UNKNOWN)
        }
    }

    override suspend fun extractSystemActions(response: AIResponse): List<SystemAction> {
        return extractSystemActions(response.text)
    }

    private fun extractSystemActions(responseText: String): List<SystemAction> {
        val actions = mutableListOf<SystemAction>()
        val lowerText = responseText.lowercase()
        
        // Extract actions based on response content
        when {
            lowerText.contains("open") && lowerText.contains("app") -> {
                val appName = extractAppName(lowerText)
                if (appName.isNotEmpty()) {
                    actions.add(
                        SystemAction(
                            type = ActionType.OPEN_APP,
                            target = appName,
                            parameters = mapOf("app_name" to appName)
                        )
                    )
                }
            }
            lowerText.contains("turn on wifi") || lowerText.contains("enable wifi") -> {
                actions.add(
                    SystemAction(
                        type = ActionType.TOGGLE_WIFI,
                        target = "wifi",
                        parameters = mapOf("state" to "on"),
                        requiresConfirmation = true
                    )
                )
            }
            lowerText.contains("turn off wifi") || lowerText.contains("disable wifi") -> {
                actions.add(
                    SystemAction(
                        type = ActionType.TOGGLE_WIFI,
                        target = "wifi",
                        parameters = mapOf("state" to "off"),
                        requiresConfirmation = true
                    )
                )
            }
            // Add more action extraction logic
        }
        
        return actions
    }

    /**
     * Post-process AI response to make it more suitable for voice output
     */
    private fun processResponseForVoice(response: String, responseStyle: Constants.AIResponseStyle): String {
        var processed = response.trim()

        // For brief and conversational styles, limit response length
        if (responseStyle == Constants.AIResponseStyle.BRIEF || responseStyle == Constants.AIResponseStyle.CONVERSATIONAL) {
            // Split into sentences and take only the first 2-3 for voice
            val sentences = processed.split(". ", "! ", "? ")
                .filter { it.isNotBlank() }
                .take(3)

            processed = sentences.joinToString(". ") { sentence ->
                if (!sentence.endsWith(".") && !sentence.endsWith("!") && !sentence.endsWith("?")) {
                    "$sentence."
                } else {
                    sentence
                }
            }
        }

        // Clean up common issues for voice output
        processed = processed
            .replace("\\n", " ") // Remove line breaks
            .replace("  ", " ") // Remove double spaces
            .replace("*", "") // Remove markdown formatting
            .replace("#", "") // Remove markdown headers
            .trim()

        // Ensure proper ending punctuation for TTS
        if (!processed.endsWith(".") && !processed.endsWith("!") && !processed.endsWith("?")) {
            processed += "."
        }

        return processed
    }

    override suspend fun generateCohereResponse(
        prompt: String,
        maxTokens: Int,
        temperature: Float
    ): Result<String> {
        return try {
            val request = CohereGenerateRequest(
                prompt = prompt,
                max_tokens = maxTokens,
                temperature = temperature.toDouble()
            )
            
            val response = cohereApiService.generateText(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.generations.isNotEmpty()) {
                    Result.success(body.generations.first().text.trim())
                } else {
                    Result.failure(Exception("Empty response from Cohere"))
                }
            } else {
                Result.failure(Exception("Cohere API error: ${response.code()}"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error calling Cohere API", e)
            Result.failure(e)
        }
    }

    override suspend fun searchPerplexity(query: String, maxTokens: Int): Result<String> {
        return try {
            val messages = listOf(
                PerplexityMessage(role = "system", content = "You are Zara, a helpful AI assistant. Provide concise, accurate responses."),
                PerplexityMessage(role = "user", content = query)
            )
            
            val request = PerplexityChatRequest(
                messages = messages,
                max_tokens = maxTokens
            )
            
            val response = perplexityApiService.chatCompletion(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.choices.isNotEmpty()) {
                    Result.success(body.choices.first().message.content.trim())
                } else {
                    Result.failure(Exception("Empty response from Perplexity"))
                }
            } else {
                Result.failure(Exception("Perplexity API error: ${response.code()}"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error calling Perplexity API", e)
            Result.failure(e)
        }
    }

    override suspend fun buildContextPrompt(
        command: String,
        conversationHistory: List<String>,
        personality: Constants.AIPersonality,
        responseStyle: Constants.AIResponseStyle
    ): String {
        val personalityPrompt = when (personality) {
            Constants.AIPersonality.PROFESSIONAL -> "You are a professional AI voice assistant named Zara. Be formal, precise, and helpful."
            Constants.AIPersonality.FRIENDLY -> "You are a friendly AI voice assistant named Zara. Be warm, approachable, and conversational."
            Constants.AIPersonality.CASUAL -> "You are a casual AI voice assistant named Zara. Be relaxed, informal, and easy-going."
        }

        val stylePrompt = when (responseStyle) {
            Constants.AIResponseStyle.BRIEF -> "Keep responses very short - 1-2 sentences maximum. Be direct and concise."
            Constants.AIResponseStyle.DETAILED -> "Provide comprehensive responses with explanations and context."
            Constants.AIResponseStyle.CONVERSATIONAL -> "Give natural, human-like responses. Keep answers short and conversational - like talking to a friend. Aim for 1-3 sentences maximum. Avoid long explanations unless specifically asked."
        }
        
        val historyContext = if (conversationHistory.isNotEmpty()) {
            "Previous conversation:\n${conversationHistory.takeLast(5).joinToString("\n")}\n\n"
        } else ""
        
        return """
            $personalityPrompt $stylePrompt

            Important: This is a voice conversation. Your response will be spoken aloud, so keep it natural and conversational. Avoid long paragraphs or lists unless specifically requested.

            $historyContext

            User: $command

            Zara:
        """.trimIndent()
    }

    override suspend fun summarizeConversation(messages: List<String>): Result<String> {
        return try {
            val conversationText = messages.joinToString("\n")
            val prompt = "Summarize this conversation in 2-3 sentences:\n\n$conversationText"
            
            generateCohereResponse(prompt, maxTokens = 100, temperature = 0.3f)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error summarizing conversation", e)
            Result.failure(e)
        }
    }

    override suspend fun getCachedResponse(commandHash: String): AIResponse? {
        return responseCache[commandHash]
    }

    override suspend fun cacheResponse(commandHash: String, response: AIResponse) {
        // Limit cache size
        if (responseCache.size >= 100) {
            responseCache.clear()
        }
        responseCache[commandHash] = response
    }

    override suspend fun clearResponseCache() {
        responseCache.clear()
    }

    override suspend fun checkCohereHealth(): Result<Boolean> {
        return try {
            val response = cohereApiService.generateText(
                CohereGenerateRequest(prompt = "Hello", max_tokens = 5)
            )
            Result.success(response.isSuccessful)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun checkPerplexityHealth(): Result<Boolean> {
        return try {
            val response = perplexityApiService.chatCompletion(
                PerplexityChatRequest(
                    messages = listOf(PerplexityMessage("user", "Hello")),
                    max_tokens = 5
                )
            )
            Result.success(response.isSuccessful)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getPreferredAISource(): Flow<AISource> = _preferredAISource.asStateFlow()

    override suspend fun setPreferredAISource(source: AISource) {
        _preferredAISource.value = source
    }

    override fun streamResponse(command: VoiceCommand): Flow<String> {
        // Implementation for streaming responses would go here
        // For now, return empty flow
        return kotlinx.coroutines.flow.emptyFlow()
    }

    override suspend fun getFallbackResponse(command: VoiceCommand): AIResponse {
        return AIResponse(
            id = UUID.randomUUID().toString(),
            text = "I'm sorry, I'm having trouble processing your request right now. Please try again later.",
            timestamp = Date(),
            commandId = command.id,
            responseTime = 0L,
            source = AISource.LOCAL,
            confidence = 0f
        )
    }

    override suspend fun handleAPIError(error: Throwable, command: VoiceCommand): AIResponse {
        val errorMessage = when {
            error.message?.contains("network", ignoreCase = true) == true -> 
                "I'm having trouble connecting to the internet. Please check your connection and try again."
            error.message?.contains("timeout", ignoreCase = true) == true -> 
                "The request is taking too long. Please try again."
            else -> "I encountered an error while processing your request. Please try again."
        }
        
        return AIResponse(
            id = UUID.randomUUID().toString(),
            text = errorMessage,
            timestamp = Date(),
            commandId = command.id,
            responseTime = 0L,
            source = AISource.LOCAL,
            confidence = 0f,
            metadata = mapOf("error" to (error.message ?: "Unknown error"))
        )
    }

    // Helper methods
    private fun generateCacheKey(text: String, personality: Constants.AIPersonality, style: Constants.AIResponseStyle): String {
        return "${text.hashCode()}_${personality.name}_${style.name}"
    }

    private fun containsSystemKeywords(text: String): Boolean {
        val systemKeywords = listOf("open", "close", "turn on", "turn off", "enable", "disable", "set", "adjust")
        return systemKeywords.any { text.contains(it) }
    }

    private fun containsInformationKeywords(text: String): Boolean {
        val infoKeywords = listOf("what", "when", "where", "how", "why", "weather", "news", "search")
        return infoKeywords.any { text.contains(it) }
    }

    private fun containsCommunicationKeywords(text: String): Boolean {
        val commKeywords = listOf("call", "message", "text", "email", "contact")
        return commKeywords.any { text.contains(it) }
    }

    private fun containsEntertainmentKeywords(text: String): Boolean {
        val entKeywords = listOf("play", "music", "song", "video", "movie", "pause", "stop", "next", "previous")
        return entKeywords.any { text.contains(it) }
    }

    private fun extractAppName(text: String): String {
        // Simple app name extraction - could be improved with NLP
        val words = text.split(" ")
        val openIndex = words.indexOfFirst { it.contains("open") }
        if (openIndex != -1 && openIndex < words.size - 1) {
            return words[openIndex + 1].replace(Regex("[^a-zA-Z]"), "")
        }
        return ""
    }

    private suspend fun handleSystemCommand(command: VoiceCommand): Result<AIResponse> {
        // Handle system-specific commands
        return generateResponse(command)
    }

    private suspend fun handleInformationQuery(command: VoiceCommand): Result<AIResponse> {
        // Use Perplexity for information queries
        return try {
            val response = searchPerplexity(command.text).getOrThrow()
            Result.success(
                AIResponse(
                    id = UUID.randomUUID().toString(),
                    text = response,
                    timestamp = Date(),
                    commandId = command.id,
                    responseTime = System.currentTimeMillis() - command.timestamp.time,
                    source = AISource.PERPLEXITY,
                    confidence = command.confidence
                )
            )
        } catch (e: Exception) {
            generateResponse(command)
        }
    }

    private suspend fun handleCommunicationCommand(command: VoiceCommand): Result<AIResponse> {
        // Handle communication commands
        return generateResponse(command)
    }

    private suspend fun handleEntertainmentCommand(command: VoiceCommand): Result<AIResponse> {
        // Handle entertainment commands
        return generateResponse(command)
    }
}
