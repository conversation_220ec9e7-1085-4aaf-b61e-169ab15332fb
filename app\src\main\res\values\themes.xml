<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base Zara Theme -->
    <style name="Theme.Zara" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/deep_teal</item>
        <item name="colorPrimaryDark">@color/deep_teal</item>
        <item name="colorAccent">@color/soft_coral</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/neuro_background</item>
        <item name="android:textColorPrimary">@color/text_primary_light</item>
        <item name="android:textColorSecondary">@color/text_secondary_light</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window properties -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <!-- Neumorphism Button Style -->
    <style name="NeumorphismButton">
        <item name="android:background">@drawable/neuro_button_background</item>
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingHorizontal">24dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- Neumorphism Card Style -->
    <style name="NeumorphismCard">
        <item name="android:background">@drawable/neuro_card_background</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:padding">16dp</item>
    </style>

    <!-- Voice Interaction Button -->
    <style name="VoiceButton" parent="NeumorphismButton">
        <item name="android:background">@drawable/voice_button_background</item>
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:textSize">0sp</item>
    </style>

    <!-- Settings Item Style -->
    <style name="SettingsItem">
        <item name="android:background">@drawable/neuro_settings_item_background</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_marginHorizontal">16dp</item>
        <item name="android:layout_marginVertical">4dp</item>
    </style>

    <!-- Base Text Appearance -->
    <style name="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@android:color/black</item>
    </style>

    <style name="TextAppearance.Zara" parent="TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/text_primary_light</item>
    </style>

    <!-- Text Styles -->
    <style name="TextAppearance.Zara.Headline1" parent="TextAppearance.Zara">
        <item name="android:textSize">32sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.Zara.Headline2" parent="TextAppearance.Zara">
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.Zara.Body1" parent="TextAppearance.Zara">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.Zara.Body2" parent="TextAppearance.Zara">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style>

    <style name="TextAppearance.Zara.Caption" parent="TextAppearance.Zara">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/text_tertiary_light</item>
    </style>

    <!-- Voice State Text -->
    <style name="VoiceStateText" parent="TextAppearance.Zara.Body1">
        <item name="android:textAlignment">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/text_secondary_light</item>
    </style>

    <!-- Switch Style -->
    <style name="NeumorphismSwitch">
        <item name="android:thumb">@drawable/switch_thumb</item>
        <item name="android:track">@drawable/switch_track</item>
    </style>

    <!-- Slider Style -->
    <style name="NeumorphismSlider">
        <item name="android:background">@drawable/slider_background</item>
    </style>

    <!-- Dialog Style -->
    <style name="NeumorphismDialog">
        <item name="android:background">@drawable/dialog_background</item>
        <item name="android:windowBackground">@color/transparent</item>
    </style>

    <!-- Bottom Sheet Style -->
    <style name="NeumorphismBottomSheet">
        <item name="android:background">@drawable/bottom_sheet_background</item>
    </style>

    <!-- Floating Action Button Style -->
    <style name="NeumorphismFAB">
        <item name="android:background">@drawable/fab_background</item>
        <item name="android:elevation">0dp</item>
    </style>
</resources>
