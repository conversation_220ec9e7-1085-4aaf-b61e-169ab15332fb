package com.zara.assistant.di

import android.content.Context
import androidx.room.Room
import com.zara.assistant.core.Constants
import com.zara.assistant.data.local.database.ZaraDatabase
import com.zara.assistant.data.local.dao.ConversationDao
import com.zara.assistant.data.local.dao.SettingsDao
import com.zara.assistant.data.local.dao.CommandDao
import com.zara.assistant.data.local.dao.UserLearningDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dependency injection module for database-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideZaraDatabase(@ApplicationContext context: Context): ZaraDatabase {
        return Room.databaseBuilder(
            context,
            ZaraDatabase::class.java,
            Constants.Database.DATABASE_NAME
        )
        .fallbackToDestructiveMigration()
        .build()
    }

    @Provides
    fun provideConversationDao(database: ZaraDatabase): ConversationDao {
        return database.conversationDao()
    }

    @Provides
    fun provideSettingsDao(database: ZaraDatabase): SettingsDao {
        return database.settingsDao()
    }

    @Provides
    fun provideCommandDao(database: ZaraDatabase): CommandDao {
        return database.commandDao()
    }

    @Provides
    fun provideUserLearningDao(database: ZaraDatabase): UserLearningDao {
        return database.userLearningDao()
    }
}
