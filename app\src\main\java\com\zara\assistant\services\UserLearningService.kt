package com.zara.assistant.services

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import com.zara.assistant.data.local.dao.UserLearningDao
import com.zara.assistant.domain.model.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*
import javax.inject.Inject

/**
 * Background service for user learning and pattern recognition
 */
@AndroidEntryPoint
class UserLearningService : Service() {
    
    companion object {
        private const val TAG = "UserLearningService"
        const val ACTION_LOG_INTERACTION = "LOG_INTERACTION"
        const val ACTION_ANALYZE_PATTERNS = "ANALYZE_PATTERNS"
        const val ACTION_GENERATE_SUGGESTIONS = "GENERATE_SUGGESTIONS"
        
        // Intent extras
        const val EXTRA_COMMAND = "command"
        const val EXTRA_RESPONSE = "response"
        const val EXTRA_SUCCESS = "success"
        const val EXTRA_EXECUTION_TIME = "execution_time"
    }
    
    @Inject
    lateinit var userLearningDao: UserLearningDao
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    private val _suggestions = MutableStateFlow<List<ProactiveSuggestion>>(emptyList())
    val suggestions: StateFlow<List<ProactiveSuggestion>> = _suggestions.asStateFlow()
    
    private val _learningStats = MutableStateFlow(LearningStats())
    val learningStats: StateFlow<LearningStats> = _learningStats.asStateFlow()
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_LOG_INTERACTION -> {
                val command = intent.getStringExtra(EXTRA_COMMAND) ?: return START_NOT_STICKY
                val response = intent.getStringExtra(EXTRA_RESPONSE) ?: ""
                val success = intent.getBooleanExtra(EXTRA_SUCCESS, true)
                val executionTime = intent.getLongExtra(EXTRA_EXECUTION_TIME, 0)
                
                logUserInteraction(command, response, success, executionTime)
            }
            ACTION_ANALYZE_PATTERNS -> {
                analyzePatterns()
            }
            ACTION_GENERATE_SUGGESTIONS -> {
                generateProactiveSuggestions()
            }
        }
        return START_STICKY
    }
    
    /**
     * Log a user interaction for learning
     */
    private fun logUserInteraction(command: String, response: String, success: Boolean, executionTime: Long) {
        serviceScope.launch {
            try {
                val calendar = Calendar.getInstance()
                val context = getCurrentContext()
                
                val interaction = UserInteraction(
                    command = command,
                    response = response,
                    timestamp = System.currentTimeMillis(),
                    timeOfDay = calendar.get(Calendar.HOUR_OF_DAY),
                    dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK),
                    success = success,
                    executionTimeMs = executionTime,
                    contextData = serializeContext(context)
                )
                
                userLearningDao.insertInteraction(interaction)
                Log.d(TAG, "✅ Logged interaction: $command")
                
                // Trigger pattern analysis if we have enough data
                val recentCount = userLearningDao.getInteractionCountSince(
                    System.currentTimeMillis() - 24 * 60 * 60 * 1000 // Last 24 hours
                )
                
                if (recentCount % 10 == 0) { // Analyze patterns every 10 interactions
                    analyzePatterns()
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error logging interaction", e)
            }
        }
    }
    
    /**
     * Analyze user patterns and update pattern database
     */
    private fun analyzePatterns() {
        serviceScope.launch {
            try {
                Log.d(TAG, "🧠 Analyzing user patterns...")
                
                // Analyze time-based patterns
                analyzeTimeBasedPatterns()
                
                // Analyze frequency patterns
                analyzeFrequencyPatterns()
                
                // Update learning stats
                updateLearningStats()
                
                Log.d(TAG, "✅ Pattern analysis complete")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error analyzing patterns", e)
            }
        }
    }
    
    /**
     * Analyze time-based patterns (e.g., "User opens Spotify at 7 PM daily")
     */
    private suspend fun analyzeTimeBasedPatterns() {
        val calendar = Calendar.getInstance()
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        val currentDay = calendar.get(Calendar.DAY_OF_WEEK)
        
        // Look for patterns in the last 7 days
        val weekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        val interactions = userLearningDao.getInteractionsByTimeRange(weekAgo, System.currentTimeMillis())
        
        // Group by command and time
        val timePatterns = interactions
            .filter { it.success }
            .groupBy { "${it.command}_${it.timeOfDay}" }
            .filter { it.value.size >= 3 } // At least 3 occurrences
        
        timePatterns.forEach { (key: String, occurrences: List<UserInteraction>) ->
            val command = key.substringBefore("_")
            val hour = key.substringAfter("_").toInt()

            val confidence = calculateTimePatternConfidence(occurrences, hour)
            
            if (confidence > 0.6f) {
                val pattern = UserPattern(
                    patternType = PatternType.TIME_BASED,
                    description = "User typically uses '$command' around ${formatHour(hour)}",
                    confidence = confidence,
                    frequency = occurrences.size,
                    lastSeen = occurrences.maxOf { it.timestamp },
                    metadata = """{"command":"$command","hour":$hour}"""
                )
                
                userLearningDao.insertPattern(pattern)
                Log.d(TAG, "📊 Found time pattern: ${pattern.description} (confidence: $confidence)")
            }
        }
    }
    
    /**
     * Analyze frequency-based patterns
     */
    private suspend fun analyzeFrequencyPatterns() {
        val frequentCommands = userLearningDao.getMostFrequentCommands(10)
        
        frequentCommands.forEach { commandFreq: com.zara.assistant.data.local.dao.CommandFrequency ->
            if (commandFreq.frequency >= 5) {
                val pattern = UserPattern(
                    patternType = PatternType.FREQUENCY_BASED,
                    description = "User frequently uses '${commandFreq.command}' (${commandFreq.frequency} times)",
                    confidence = minOf(commandFreq.frequency / 20.0f, 1.0f),
                    frequency = commandFreq.frequency,
                    lastSeen = System.currentTimeMillis(),
                    metadata = """{"command":"${commandFreq.command}"}"""
                )
                
                userLearningDao.insertPattern(pattern)
            }
        }
    }
    
    /**
     * Generate proactive suggestions based on learned patterns
     */
    private fun generateProactiveSuggestions() {
        serviceScope.launch {
            try {
                val calendar = Calendar.getInstance()
                val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
                val currentDay = calendar.get(Calendar.DAY_OF_WEEK)
                
                val suggestions = mutableListOf<ProactiveSuggestion>()
                val patterns = userLearningDao.getHighConfidencePatterns(0.7f)
                
                patterns.forEach { pattern: UserPattern ->
                    when (pattern.patternType) {
                        PatternType.TIME_BASED -> {
                            val suggestion = generateTimeSuggestion(pattern, currentHour)
                            if (suggestion != null) suggestions.add(suggestion)
                        }
                        PatternType.FREQUENCY_BASED -> {
                            val suggestion = generateFrequencySuggestion(pattern)
                            if (suggestion != null) suggestions.add(suggestion)
                        }
                    }
                }
                
                _suggestions.value = suggestions.take(3) // Limit to 3 suggestions
                Log.d(TAG, "💡 Generated ${suggestions.size} proactive suggestions")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error generating suggestions", e)
            }
        }
    }
    
    private fun getCurrentContext(): InteractionContext {
        val calendar = Calendar.getInstance()
        return InteractionContext(
            timeOfDay = calendar.get(Calendar.HOUR_OF_DAY),
            dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        )
    }
    
    private fun serializeContext(context: InteractionContext): String {
        // Simple JSON serialization - in production, use proper JSON library
        return """{"timeOfDay":${context.timeOfDay},"dayOfWeek":${context.dayOfWeek}}"""
    }
    
    private fun calculateTimePatternConfidence(occurrences: List<UserInteraction>, targetHour: Int): Float {
        val hourVariance = occurrences.map { kotlin.math.abs(it.timeOfDay - targetHour) }.average()
        return maxOf(0f, 1f - (hourVariance / 12f).toFloat()) // Lower variance = higher confidence
    }
    
    private fun formatHour(hour: Int): String {
        return when {
            hour == 0 -> "midnight"
            hour < 12 -> "${hour} AM"
            hour == 12 -> "noon"
            else -> "${hour - 12} PM"
        }
    }
    
    private fun generateTimeSuggestion(pattern: UserPattern, currentHour: Int): ProactiveSuggestion? {
        // Parse pattern metadata to get command and hour
        val metadata = pattern.metadata ?: return null
        // Simple parsing - in production, use proper JSON parsing
        val command = metadata.substringAfter("\"command\":\"").substringBefore("\"")
        val patternHour = metadata.substringAfter("\"hour\":").substringBefore("}").toIntOrNull() ?: return null
        
        // Suggest if current time is close to pattern time
        if (kotlin.math.abs(currentHour - patternHour) <= 1) {
            return ProactiveSuggestion(
                title = "Ready for your usual $command?",
                description = "I notice you typically use this around ${formatHour(patternHour)}",
                command = command,
                confidence = pattern.confidence,
                basedOnPattern = pattern.description
            )
        }
        return null
    }
    
    private fun generateFrequencySuggestion(pattern: UserPattern): ProactiveSuggestion? {
        val metadata = pattern.metadata ?: return null
        val command = metadata.substringAfter("\"command\":\"").substringBefore("\"")
        
        return ProactiveSuggestion(
            title = "Quick access to $command",
            description = "This is one of your most used commands",
            command = command,
            confidence = pattern.confidence,
            basedOnPattern = pattern.description
        )
    }
    
    private suspend fun updateLearningStats() {
        val weekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        val interactionCount = userLearningDao.getInteractionCountSince(weekAgo)
        val successRate = userLearningDao.getSuccessRate(weekAgo) ?: 0.0
        val patternCount = userLearningDao.getActivePatterns().size
        
        _learningStats.value = LearningStats(
            totalInteractions = interactionCount,
            successRate = successRate.toFloat(),
            activePatterns = patternCount,
            lastAnalysis = System.currentTimeMillis()
        )
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        Log.d(TAG, "UserLearningService destroyed")
    }
}

/**
 * Learning statistics data class
 */
data class LearningStats(
    val totalInteractions: Int = 0,
    val successRate: Float = 0f,
    val activePatterns: Int = 0,
    val lastAnalysis: Long = 0
)
