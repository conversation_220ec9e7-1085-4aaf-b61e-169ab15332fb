package com.zara.assistant.ui.screens

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.zara.assistant.R
import com.zara.assistant.presentation.components.NeumorphismButton
import kotlinx.coroutines.launch

/**
 * Onboarding screen to introduce users to Zara
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun OnboardingScreen(
    onNavigateToPermissions: () -> Unit,
    onNavigateToMain: () -> Unit,
    modifier: Modifier = Modifier
) {
    val pagerState = rememberPagerState(pageCount = { 3 })
    val scope = rememberCoroutineScope()

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        // Pager content
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.weight(1f)
        ) { page ->
            OnboardingPage(
                page = page,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Page indicators
        PageIndicators(
            pageCount = 3,
            currentPage = pagerState.currentPage,
            modifier = Modifier.padding(vertical = 16.dp)
        )

        // Navigation buttons
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (pagerState.currentPage < 2) {
                NeumorphismButton(
                    text = stringResource(R.string.next),
                    onClick = {
                        scope.launch {
                            pagerState.animateScrollToPage(pagerState.currentPage + 1)
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                NeumorphismButton(
                    text = stringResource(R.string.skip),
                    onClick = onNavigateToPermissions,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                NeumorphismButton(
                    text = "Get Started",
                    onClick = onNavigateToPermissions,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun OnboardingPage(
    page: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(horizontal = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Icon
        Image(
            painter = painterResource(
                when (page) {
                    0 -> R.drawable.ic_voice_assistant
                    1 -> R.drawable.ic_microphone
                    else -> R.drawable.ic_settings
                }
            ),
            contentDescription = null,
            modifier = Modifier.size(120.dp)
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Title
        Text(
            text = when (page) {
                0 -> "Meet Zara"
                1 -> "Voice Control"
                else -> "Smart Assistant"
            },
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Description
        Text(
            text = when (page) {
                0 -> "Your intelligent AI voice assistant that understands and responds to your commands naturally."
                1 -> "Just say \"Hey Zara\" to start a conversation. Control your device, get information, and more."
                else -> "Zara learns your preferences and helps you be more productive with smart suggestions and automation."
            },
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun PageIndicators(
    pageCount: Int,
    currentPage: Int,
    modifier: Modifier = Modifier
) {
    androidx.compose.foundation.layout.Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        repeat(pageCount) { index ->
            androidx.compose.foundation.Canvas(
                modifier = Modifier.size(8.dp)
            ) {
                drawCircle(
                    color = if (index == currentPage) {
                        androidx.compose.ui.graphics.Color(0xFF006B5C)
                    } else {
                        androidx.compose.ui.graphics.Color(0xFFBEC9C5)
                    }
                )
            }
        }
    }
}
