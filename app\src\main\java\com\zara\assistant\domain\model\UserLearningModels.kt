package com.zara.assistant.domain.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

/**
 * User interaction data for learning and pattern recognition
 */
@Entity(tableName = "user_interactions")
data class UserInteraction(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val command: String,
    val response: String,
    val timestamp: Long,
    val timeOfDay: Int, // Hour 0-23
    val dayOfWeek: Int, // 1-7 (Calendar.SUNDAY = 1)
    val success: Boolean,
    val executionTimeMs: Long,
    val contextData: String? = null // JSON for additional context
)

/**
 * Recognized user patterns
 */
@Entity(tableName = "user_patterns")
data class UserPattern(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val patternType: String, // TIME_BASED, FREQUENCY_BASED, CONTEXT_BASED, SEQUENCE_BASED
    val description: String,
    val confidence: Float, // 0.0 to 1.0
    val frequency: Int,
    val lastSeen: Long,
    val isActive: Boolean = true,
    val metadata: String? = null // JSON for pattern-specific data
)

/**
 * User preferences learned from interactions
 */
@Entity(tableName = "user_preferences")
data class UserPreference(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val preferenceKey: String, // e.g., "default_volume", "preferred_brightness"
    val preferenceValue: String,
    val confidence: Float,
    val source: String, // How this preference was learned
    val lastUpdated: Long
)

/**
 * Proactive suggestions generated by the learning engine
 */
data class ProactiveSuggestion(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String,
    val command: String, // The command to execute if user accepts
    val confidence: Float,
    val basedOnPattern: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Context information for interactions
 */
data class InteractionContext(
    val timeOfDay: Int,
    val dayOfWeek: Int,
    val appInForeground: String? = null,
    val batteryLevel: Int? = null,
    val isCharging: Boolean? = null,
    val wifiConnected: Boolean? = null,
    val location: String? = null // General location like "home", "work"
)

/**
 * Pattern types for classification
 */
object PatternType {
    const val TIME_BASED = "TIME_BASED"           // "User opens Spotify at 7 PM daily"
    const val FREQUENCY_BASED = "FREQUENCY_BASED" // "User checks weather 3 times daily"
    const val CONTEXT_BASED = "CONTEXT_BASED"     // "User turns on WiFi when arriving home"
    const val SEQUENCE_BASED = "SEQUENCE_BASED"   // "User opens Chrome then searches news"
    const val PREFERENCE_BASED = "PREFERENCE_BASED" // "User prefers volume at 75%"
}

/**
 * User profile with personal information
 */
@Entity(tableName = "user_profile")
data class UserProfile(
    @PrimaryKey
    val id: String = "default_user",
    val name: String? = null,
    val nickname: String? = null,
    val age: Int? = null,
    val occupation: String? = null,
    val location: String? = null,
    val timezone: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Complete conversation history
 */
@Entity(tableName = "conversation_history")
data class ConversationHistory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val sessionId: String,
    val userInput: String,
    val zaraResponse: String,
    val timestamp: Long,
    val conversationType: String,
    val success: Boolean,
    val contextData: String? = null,
    val sentiment: String? = null,
    val topics: String? = null // JSON array of extracted topics
)

/**
 * Web search cache for offline access
 */
@Entity(tableName = "search_cache")
data class SearchCache(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val query: String,
    val results: String, // JSON array of search results
    val timestamp: Long,
    val source: String, // "google", "bing", etc.
    val accessCount: Int = 1,
    val lastAccessed: Long = System.currentTimeMillis()
)

/**
 * User favorites and preferences
 */
@Entity(tableName = "user_favorites")
data class UserFavorite(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val category: String, // "food", "music", "apps", "activities", etc.
    val item: String,
    val confidence: Float,
    val source: String, // How this was learned
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * Behavioral patterns and analytics
 */
@Entity(tableName = "behavioral_patterns")
data class BehavioralPattern(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val patternType: String,
    val description: String,
    val confidence: Float,
    val frequency: Int,
    val timePattern: String? = null, // JSON for time-based patterns
    val contextPattern: String? = null, // JSON for context-based patterns
    val lastSeen: Long,
    val isActive: Boolean = true
)

/**
 * ML model data and training results
 */
@Entity(tableName = "ml_model_data")
data class MLModelData(
    @PrimaryKey
    val modelName: String,
    val modelVersion: String,
    val trainingData: String, // Serialized training data
    val accuracy: Float,
    val lastTrained: Long,
    val isActive: Boolean = true
)

/**
 * Contextual environment data
 */
@Entity(tableName = "contextual_data")
data class ContextualData(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val timestamp: Long,
    val batteryLevel: Int? = null,
    val isCharging: Boolean? = null,
    val wifiConnected: Boolean? = null,
    val bluetoothConnected: Boolean? = null,
    val foregroundApp: String? = null,
    val location: String? = null,
    val weatherCondition: String? = null
)

/**
 * Learning configuration
 */
data class LearningConfig(
    val isEnabled: Boolean = true,
    val minInteractionsForPattern: Int = 3,
    val minConfidenceForSuggestion: Float = 0.7f,
    val maxSuggestionsPerDay: Int = 5,
    val dataRetentionDays: Int = 90,
    val enableWebSearch: Boolean = true,
    val enableMLTraining: Boolean = true,
    val enableProactiveMode: Boolean = true
)
