package com.zara.assistant.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.presentation.theme.NeuroLightHighlight
import com.zara.assistant.presentation.theme.NeuroLightShadow
import com.zara.assistant.presentation.theme.NeuroSurfaceLight

/**
 * Neumorphism style card component
 */
@Composable
fun NeumorphismCard(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 20.dp,
    elevation: Dp = 8.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    contentPadding: Dp = 20.dp,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(cornerRadius))
            .drawBehind {
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    
                    // Draw shadow (bottom-right)
                    frameworkPaint.color = shadowColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        elevation.toPx(),
                        elevation.toPx(),
                        elevation.toPx(),
                        shadowColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = elevation.toPx(),
                        top = elevation.toPx(),
                        right = size.width,
                        bottom = size.height,
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                    
                    // Draw highlight (top-left)
                    frameworkPaint.color = highlightColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        elevation.toPx(),
                        -elevation.toPx(),
                        -elevation.toPx(),
                        highlightColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = 0f,
                        top = 0f,
                        right = size.width - elevation.toPx(),
                        bottom = size.height - elevation.toPx(),
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(contentPadding)
    ) {
        content()
    }
}

/**
 * Inset neumorphism card (pressed/concave effect)
 */
@Composable
fun NeumorphismInsetCard(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 20.dp,
    elevation: Dp = 8.dp,
    backgroundColor: Color = NeuroSurfaceLight,
    shadowColor: Color = NeuroLightShadow,
    highlightColor: Color = NeuroLightHighlight,
    contentPadding: Dp = 20.dp,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(cornerRadius))
            .drawBehind {
                drawIntoCanvas { canvas ->
                    val paint = Paint()
                    val frameworkPaint = paint.asFrameworkPaint()
                    val insetDepth = elevation.toPx() * 0.5f
                    
                    // Draw inset shadow (top-left, inside)
                    frameworkPaint.color = shadowColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        insetDepth,
                        -insetDepth,
                        -insetDepth,
                        shadowColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = insetDepth,
                        top = insetDepth,
                        right = size.width - insetDepth,
                        bottom = size.height - insetDepth,
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                    
                    // Draw inset highlight (bottom-right, inside)
                    frameworkPaint.color = highlightColor.toArgb()
                    frameworkPaint.setShadowLayer(
                        insetDepth,
                        insetDepth,
                        insetDepth,
                        highlightColor.toArgb()
                    )
                    canvas.drawRoundRect(
                        left = insetDepth,
                        top = insetDepth,
                        right = size.width - insetDepth,
                        bottom = size.height - insetDepth,
                        radiusX = cornerRadius.toPx(),
                        radiusY = cornerRadius.toPx(),
                        paint = paint
                    )
                }
            }
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(contentPadding)
    ) {
        content()
    }
}
