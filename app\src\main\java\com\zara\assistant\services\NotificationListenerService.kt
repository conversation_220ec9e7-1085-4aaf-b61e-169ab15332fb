package com.zara.assistant.services

import android.app.Notification
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Service for listening to and managing notifications
 */
class NotificationListenerService : NotificationListenerService() {

    companion object {
        private const val TAG = "NotificationListener"
        
        fun isEnabled(context: Context): Boolean {
            val enabledListeners = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            val componentName = ComponentName(context, NotificationListenerService::class.java)
            return enabledListeners?.contains(componentName.flattenToString()) == true
        }
        
        fun requestPermission(context: Context) {
            val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    private val _notifications = MutableStateFlow<List<ZaraNotification>>(emptyList())
    val notifications: StateFlow<List<ZaraNotification>> = _notifications.asStateFlow()
    
    private val _newNotification = MutableStateFlow<ZaraNotification?>(null)
    val newNotification: StateFlow<ZaraNotification?> = _newNotification.asStateFlow()
    
    private var isServiceReady = false

    override fun onListenerConnected() {
        super.onListenerConnected()
        isServiceReady = true
        Log.d(TAG, "Notification Listener Service connected")
        
        // Load existing notifications
        loadActiveNotifications()
    }

    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        isServiceReady = false
        Log.d(TAG, "Notification Listener Service disconnected")
    }

    override fun onNotificationPosted(sbn: StatusBarNotification?) {
        super.onNotificationPosted(sbn)
        
        sbn?.let { notification ->
            val zaraNotification = convertToZaraNotification(notification)
            if (zaraNotification != null && shouldProcessNotification(zaraNotification)) {
                serviceScope.launch {
                    addNotification(zaraNotification)
                    _newNotification.value = zaraNotification
                    
                    // Broadcast new notification
                    val intent = Intent("com.zara.assistant.NEW_NOTIFICATION").apply {
                        putExtra("title", zaraNotification.title)
                        putExtra("content", zaraNotification.text)
                        putExtra("packageName", zaraNotification.packageName)
                        putExtra("timestamp", zaraNotification.timestamp)
                    }
                    sendBroadcast(intent)
                    
                    Log.d(TAG, "New notification: ${zaraNotification.title}")
                }
            }
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification?) {
        super.onNotificationRemoved(sbn)
        
        sbn?.let { notification ->
            serviceScope.launch {
                removeNotification(notification.key)
                Log.d(TAG, "Notification removed: ${notification.key}")
            }
        }
    }

    private fun loadActiveNotifications() {
        if (!isServiceReady) return
        
        try {
            val activeNotifications = activeNotifications
            val zaraNotifications = activeNotifications.mapNotNull { sbn ->
                convertToZaraNotification(sbn)
            }.filter { shouldProcessNotification(it) }
            
            _notifications.value = zaraNotifications
            Log.d(TAG, "Loaded ${zaraNotifications.size} active notifications")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading active notifications", e)
        }
    }

    private fun convertToZaraNotification(sbn: StatusBarNotification): ZaraNotification? {
        return try {
            val notification = sbn.notification
            val extras = notification.extras
            
            ZaraNotification(
                id = sbn.id,
                key = sbn.key,
                packageName = sbn.packageName,
                appName = getAppName(sbn.packageName),
                title = extras.getCharSequence(Notification.EXTRA_TITLE)?.toString() ?: "",
                text = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: "",
                bigText = extras.getCharSequence(Notification.EXTRA_BIG_TEXT)?.toString(),
                subText = extras.getCharSequence(Notification.EXTRA_SUB_TEXT)?.toString(),
                timestamp = sbn.postTime,
                isOngoing = notification.flags and Notification.FLAG_ONGOING_EVENT != 0,
                isClearable = sbn.isClearable,
                priority = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    notification.priority
                } else {
                    @Suppress("DEPRECATION")
                    notification.priority
                },
                category = notification.category,
                actions = extractNotificationActions(notification),
                canReply = hasReplyAction(notification)
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error converting notification", e)
            null
        }
    }

    private fun getAppName(packageName: String): String {
        return try {
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            packageName
        }
    }

    private fun extractNotificationActions(notification: Notification): List<NotificationAction> {
        val actions = mutableListOf<NotificationAction>()
        
        notification.actions?.forEach { action ->
            actions.add(
                NotificationAction(
                    title = action.title?.toString() ?: "",
                    actionIntent = action.actionIntent,
                    isReplyAction = isReplyAction(action)
                )
            )
        }
        
        return actions
    }

    private fun hasReplyAction(notification: Notification): Boolean {
        return notification.actions?.any { isReplyAction(it) } == true
    }

    private fun isReplyAction(action: Notification.Action): Boolean {
        return action.remoteInputs?.isNotEmpty() == true
    }

    private fun shouldProcessNotification(notification: ZaraNotification): Boolean {
        // Filter out system notifications and ongoing notifications we don't care about
        val ignoredPackages = setOf(
            "android",
            "com.android.systemui",
            "com.zara.assistant" // Don't process our own notifications
        )
        
        return !ignoredPackages.contains(notification.packageName) &&
                !notification.isOngoing &&
                notification.title.isNotEmpty()
    }

    private fun addNotification(notification: ZaraNotification) {
        val currentList = _notifications.value.toMutableList()
        
        // Remove existing notification with same key if it exists
        currentList.removeAll { it.key == notification.key }
        
        // Add new notification at the beginning
        currentList.add(0, notification)
        
        // Limit to last 50 notifications
        if (currentList.size > 50) {
            currentList.removeAt(currentList.size - 1)
        }
        
        _notifications.value = currentList
    }

    private fun removeNotification(key: String) {
        val currentList = _notifications.value.toMutableList()
        currentList.removeAll { it.key == key }
        _notifications.value = currentList
    }

    /**
     * Get notifications for reading aloud
     */
    fun getNotificationsForReading(): List<ZaraNotification> {
        return _notifications.value.filter { 
            !it.isOngoing && it.title.isNotEmpty() 
        }.take(5) // Limit to 5 most recent
    }

    /**
     * Clear a specific notification
     */
    fun clearNotification(key: String): Boolean {
        return try {
            if (isServiceReady) {
                cancelNotification(key)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing notification", e)
            false
        }
    }

    /**
     * Clear all clearable notifications
     */
    fun clearAllNotifications(): Boolean {
        return try {
            if (isServiceReady) {
                cancelAllNotifications()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all notifications", e)
            false
        }
    }

    /**
     * Reply to a notification
     */
    fun replyToNotification(key: String, replyText: String): Boolean {
        return try {
            val notification = _notifications.value.find { it.key == key }
            if (notification?.canReply == true) {
                // Implementation for replying to notifications
                // This requires handling RemoteInput
                Log.d(TAG, "Replying to notification: $replyText")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error replying to notification", e)
            false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isServiceReady = false
        Log.d(TAG, "Notification Listener Service destroyed")
    }
}

/**
 * Data class representing a notification processed by Zara
 */
data class ZaraNotification(
    val id: Int,
    val key: String,
    val packageName: String,
    val appName: String,
    val title: String,
    val text: String,
    val bigText: String? = null,
    val subText: String? = null,
    val timestamp: Long,
    val isOngoing: Boolean,
    val isClearable: Boolean,
    val priority: Int,
    val category: String? = null,
    val actions: List<NotificationAction> = emptyList(),
    val canReply: Boolean = false
)

/**
 * Data class representing a notification action
 */
data class NotificationAction(
    val title: String,
    val actionIntent: android.app.PendingIntent?,
    val isReplyAction: Boolean = false
)
