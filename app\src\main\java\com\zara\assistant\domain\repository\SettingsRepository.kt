package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.core.Constants
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for settings management
 */
interface SettingsRepository {
    
    /**
     * Voice settings
     */
    suspend fun getVoiceSettings(): VoiceSettings
    suspend fun updateVoiceSettings(settings: VoiceSettings): Result<Unit>
    fun observeVoiceSettings(): Flow<VoiceSettings>
    
    /**
     * Wake word settings
     */
    suspend fun isWakeWordEnabled(): Boolean
    suspend fun setWakeWordEnabled(enabled: Boolean): Result<Unit>
    suspend fun getWakeWordSensitivity(): Float
    suspend fun setWakeWordSensitivity(sensitivity: Float): Result<Unit>
    
    /**
     * AI settings
     */
    suspend fun getAIPersonality(): Constants.AIPersonality
    suspend fun setAIPersonality(personality: Constants.AIPersonality): Result<Unit>
    suspend fun getAIResponseStyle(): Constants.AIResponseStyle
    suspend fun setAIResponseStyle(style: Constants.AIResponseStyle): Result<Unit>
    
    /**
     * Privacy settings
     */
    suspend fun isConversationHistoryEnabled(): Boolean
    suspend fun setConversationHistoryEnabled(enabled: Boolean): Result<Unit>
    suspend fun isAnalyticsEnabled(): Boolean
    suspend fun setAnalyticsEnabled(enabled: Boolean): Result<Unit>
    suspend fun isVoiceDataStorageEnabled(): Boolean
    suspend fun setVoiceDataStorageEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * Accessibility settings
     */
    suspend fun isAccessibilityServiceEnabled(): Boolean
    suspend fun setAccessibilityServiceEnabled(enabled: Boolean): Result<Unit>
    suspend fun isNotificationAccessEnabled(): Boolean
    suspend fun setNotificationAccessEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * Language and localization
     */
    suspend fun getVoiceLanguage(): String
    suspend fun setVoiceLanguage(language: String): Result<Unit>
    suspend fun getSupportedLanguages(): List<String>
    
    /**
     * App preferences
     */
    suspend fun isFirstLaunch(): Boolean
    suspend fun setFirstLaunchCompleted(): Result<Unit>
    suspend fun getThemeMode(): ThemeMode
    suspend fun setThemeMode(mode: ThemeMode): Result<Unit>
    
    /**
     * Backup and restore
     */
    suspend fun exportSettings(): Result<String>
    suspend fun importSettings(data: String): Result<Unit>
    suspend fun resetToDefaults(): Result<Unit>
    
    /**
     * Notification settings
     */
    suspend fun areNotificationsEnabled(): Boolean
    suspend fun setNotificationsEnabled(enabled: Boolean): Result<Unit>
    suspend fun getNotificationChannels(): List<String>
    suspend fun isChannelEnabled(channelId: String): Boolean
    suspend fun setChannelEnabled(channelId: String, enabled: Boolean): Result<Unit>
}

/**
 * Theme mode options
 */
enum class ThemeMode {
    LIGHT,
    DARK,
    SYSTEM
}
