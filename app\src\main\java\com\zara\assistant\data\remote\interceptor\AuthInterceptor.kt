package com.zara.assistant.data.remote.interceptor

import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject

/**
 * Interceptor for adding authentication headers to API requests
 */
class AuthInterceptor @Inject constructor(
    private val headerName: String,
    private val headerValue: String
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        val authenticatedRequest = originalRequest.newBuilder()
            .header(headerName, headerValue)
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .header("User-Agent", "Zara-Assistant/1.0")
            .build()
        
        return chain.proceed(authenticatedRequest)
    }
}
