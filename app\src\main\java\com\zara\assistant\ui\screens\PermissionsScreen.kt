package com.zara.assistant.ui.screens

import android.Manifest
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.zara.assistant.R
import com.zara.assistant.presentation.components.NeumorphismButton
import com.zara.assistant.presentation.components.NeumorphismCard

/**
 * Screen for requesting and managing permissions
 */
@Composable
fun PermissionsScreen(
    onPermissionsGranted: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var permissionStates by remember { mutableStateOf(mapOf<String, Boolean>()) }
    
    val requiredPermissions = listOf(
        PermissionInfo(
            permission = Manifest.permission.RECORD_AUDIO,
            title = stringResource(R.string.permission_microphone_title),
            description = stringResource(R.string.permission_microphone_description),
            isRequired = true
        ),
        PermissionInfo(
            permission = Manifest.permission.POST_NOTIFICATIONS,
            title = stringResource(R.string.permission_notification_title),
            description = stringResource(R.string.permission_notification_description),
            isRequired = true
        ),
        PermissionInfo(
            permission = Manifest.permission.SYSTEM_ALERT_WINDOW,
            title = stringResource(R.string.permission_overlay_title),
            description = stringResource(R.string.permission_overlay_description),
            isRequired = false
        ),
        PermissionInfo(
            permission = Manifest.permission.CALL_PHONE,
            title = stringResource(R.string.permission_phone_title),
            description = stringResource(R.string.permission_phone_description),
            isRequired = false
        ),
        PermissionInfo(
            permission = Manifest.permission.ACCESS_FINE_LOCATION,
            title = stringResource(R.string.permission_location_title),
            description = stringResource(R.string.permission_location_description),
            isRequired = false
        )
    )

    // Check permission states
    LaunchedEffect(Unit) {
        val states = requiredPermissions.associate { permissionInfo ->
            permissionInfo.permission to (ContextCompat.checkSelfPermission(
                context,
                permissionInfo.permission
            ) == android.content.pm.PackageManager.PERMISSION_GRANTED)
        }
        permissionStates = states
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Text(
            text = "Permissions Required",
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Zara needs these permissions to provide the best voice assistant experience.",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Permissions list
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(requiredPermissions) { permissionInfo ->
                PermissionItem(
                    permissionInfo = permissionInfo,
                    isGranted = permissionStates[permissionInfo.permission] ?: false,
                    onRequestPermission = {
                        // Handle permission request
                        // This would typically trigger the permission request
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Action buttons
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val requiredPermissionsGranted = requiredPermissions
                .filter { it.isRequired }
                .all { permissionStates[it.permission] == true }

            if (requiredPermissionsGranted) {
                NeumorphismButton(
                    text = stringResource(R.string.continue_text),
                    onClick = onPermissionsGranted,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                NeumorphismButton(
                    text = "Grant Required Permissions",
                    onClick = {
                        // Trigger permission request
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            NeumorphismButton(
                text = "Skip for Now",
                onClick = onNavigateBack,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun PermissionItem(
    permissionInfo: PermissionInfo,
    isGranted: Boolean,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = permissionInfo.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    if (permissionInfo.isRequired) {
                        Text(
                            text = " *",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = permissionInfo.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                imageVector = if (isGranted) Icons.Default.Check else Icons.Default.Close,
                contentDescription = if (isGranted) "Granted" else "Not granted",
                tint = if (isGranted) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.error
                },
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * Data class for permission information
 */
private data class PermissionInfo(
    val permission: String,
    val title: String,
    val description: String,
    val isRequired: Boolean
)
