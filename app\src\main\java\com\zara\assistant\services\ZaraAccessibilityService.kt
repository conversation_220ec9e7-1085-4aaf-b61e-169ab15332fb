package com.zara.assistant.services

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.ComponentName
import android.content.Intent
import android.graphics.Path
import android.graphics.Rect
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Toast
import com.zara.assistant.domain.model.ActionType
import com.zara.assistant.domain.model.SystemAction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * Accessibility service for system control via voice commands
 */
class ZaraAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "ZaraAccessibilityService"
        
        fun isEnabled(context: android.content.Context): Boolean {
            val enabledServices = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            val serviceName = "${context.packageName}/${ZaraAccessibilityService::class.java.name}"
            return enabledServices?.contains(serviceName) == true
        }
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var isServiceReady = false

    // Pending toggle requests
    private var pendingWifiToggle: Boolean? = null
    private var pendingBluetoothToggle: Boolean? = null
    private var pendingAirplaneModeToggle: Boolean? = null
    private var pendingMobileDataToggle: Boolean? = null
    private var pendingLocationToggle: Boolean? = null
    private var pendingAutoRotateToggle: Boolean? = null
    private var pendingDndToggle: Boolean? = null
    private var lastToggleRequestTime = 0L

    override fun onServiceConnected() {
        super.onServiceConnected()
        isServiceReady = true
        Log.d(TAG, "✅ Zara Accessibility Service connected and ready")

        // Test accessibility service capabilities
        val serviceInfo = serviceInfo
        Log.d(TAG, "🔧 Service capabilities: ${serviceInfo?.capabilities}")
        Log.d(TAG, "🔧 Service flags: ${serviceInfo?.flags}")

        // Check for restricted settings issue
        checkRestrictedSettings()

        // Register for system action broadcasts
        // This would typically be done through a BroadcastReceiver
    }

    /**
     * Check if the app has restricted settings enabled
     */
    private fun checkRestrictedSettings() {
        try {
            // Check if we can access system settings
            val canWriteSettings = Settings.System.canWrite(this)
            Log.d(TAG, "📋 Can write system settings: $canWriteSettings")

            if (!canWriteSettings) {
                Log.w(TAG, "⚠️ RESTRICTED SETTINGS DETECTED!")
                Log.w(TAG, "📱 To enable WiFi control, please:")
                Log.w(TAG, "1. Go to Settings → Apps → Zara → App Info")
                Log.w(TAG, "2. Tap 3 dots menu → Allow Restricted Settings")
                Log.w(TAG, "3. Enter your lock code/pattern")

                // Show toast with instructions
                showToast("Enable 'Allow Restricted Settings' for Zara in App Settings for full WiFi control")
            } else {
                Log.d(TAG, "✅ Restricted settings are enabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error checking restricted settings", e)
        }
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // Handle accessibility events if needed
        event?.let {
            when (it.eventType) {
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    Log.d(TAG, "Window changed: ${it.packageName}")

                    // Check if we're in settings and need to perform smart toggles
                    if (isSettingsApp(it.packageName.toString())) {
                        serviceScope.launch {
                            kotlinx.coroutines.delay(1000) // Wait for UI to load
                            checkForPendingToggles()
                        }
                    }
                    Unit
                }
                AccessibilityEvent.TYPE_VIEW_CLICKED -> {
                    Log.d(TAG, "View clicked in: ${it.packageName}")
                }
                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                    // Settings content changed, might be a good time to check for toggles
                    if (isSettingsApp(it.packageName.toString())) {
                        serviceScope.launch {
                            kotlinx.coroutines.delay(500) // Brief delay for content to settle
                            checkForPendingToggles()
                        }
                    }
                    Unit
                }
                else -> {
                    // Handle other event types or ignore
                }
            }
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
        isServiceReady = false
    }

    /**
     * Execute a system action
     */
    fun executeSystemAction(action: SystemAction): Boolean {
        if (!isServiceReady) {
            Log.e(TAG, "Service not ready")
            return false
        }

        return try {
            when (action.type) {
                ActionType.OPEN_APP -> openApp(action.target)
                ActionType.CLOSE_APP -> closeApp(action.target)
                ActionType.SWITCH_APP -> switchToApp(action.target)
                ActionType.TOGGLE_WIFI -> toggleWifi(action.parameters["state"] == "on")
                ActionType.TOGGLE_BLUETOOTH -> toggleBluetooth(action.parameters["state"] == "on")
                ActionType.ADJUST_VOLUME -> adjustVolume(action.parameters["direction"] ?: "up")
                ActionType.SET_BRIGHTNESS -> setBrightness(action.parameters["level"]?.toIntOrNull() ?: 50)
                ActionType.TOGGLE_AIRPLANE_MODE -> toggleAirplaneMode(action.parameters["state"] == "on")
                ActionType.TOGGLE_MOBILE_DATA -> toggleMobileData(action.parameters["state"] == "on")
                ActionType.TOGGLE_LOCATION -> toggleLocation(action.parameters["state"] == "on")
                ActionType.TOGGLE_AUTO_ROTATE -> toggleAutoRotate(action.parameters["state"] == "on")
                ActionType.TOGGLE_DO_NOT_DISTURB -> toggleDoNotDisturb(action.parameters["state"] == "on")
                ActionType.TAKE_SCREENSHOT -> takeScreenshot()
                else -> {
                    Log.w(TAG, "Unsupported action type: ${action.type}")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing system action", e)
            false
        }
    }

    private fun openApp(packageName: String): Boolean {
        return try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                Log.d(TAG, "Opened app: $packageName")
                true
            } else {
                // Try to find app by name
                openAppByName(packageName)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open app: $packageName", e)
            false
        }
    }

    private fun openAppByName(appName: String): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }
            
            val apps = packageManager.queryIntentActivities(intent, 0)
            val targetApp = apps.find { 
                packageManager.getApplicationLabel(it.activityInfo.applicationInfo)
                    .toString().lowercase().contains(appName.lowercase())
            }
            
            if (targetApp != null) {
                val launchIntent = Intent().apply {
                    component = ComponentName(
                        targetApp.activityInfo.packageName,
                        targetApp.activityInfo.name
                    )
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                startActivity(launchIntent)
                Log.d(TAG, "Opened app by name: $appName")
                true
            } else {
                Log.w(TAG, "App not found: $appName")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open app by name: $appName", e)
            false
        }
    }

    private fun closeApp(packageName: String): Boolean {
        return try {
            // Use global back gesture to close current app
            performGlobalAction(GLOBAL_ACTION_BACK)
            Log.d(TAG, "Performed back action to close app")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to close app", e)
            false
        }
    }

    private fun switchToApp(packageName: String): Boolean {
        return try {
            // Open recent apps
            performGlobalAction(GLOBAL_ACTION_RECENTS)
            
            // Find and click on the target app in recents
            serviceScope.launch {
                kotlinx.coroutines.delay(500) // Wait for recents to open
                findAndClickAppInRecents(packageName)
            }
            
            Log.d(TAG, "Switched to recents for app: $packageName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to app", e)
            false
        }
    }

    private fun findAndClickAppInRecents(packageName: String) {
        val rootNode = rootInActiveWindow ?: return
        
        // Find nodes that might represent the target app
        val appNodes = findNodesByText(rootNode, packageName)
        
        if (appNodes.isNotEmpty()) {
            appNodes.first().performAction(AccessibilityNodeInfo.ACTION_CLICK)
            Log.d(TAG, "Clicked on app in recents: $packageName")
        }
        
        rootNode.recycle()
    }

    private fun findNodesByText(node: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        
        if (node.text?.toString()?.contains(text, ignoreCase = true) == true ||
            node.contentDescription?.toString()?.contains(text, ignoreCase = true) == true) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                result.addAll(findNodesByText(child, text))
                child.recycle()
            }
        }
        
        return result
    }

    private fun toggleWifi(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingWifiToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open WiFi settings
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened WiFi settings, pending toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle WiFi", e)
            false
        }
    }

    /**
     * Find WiFi toggle in settings
     */
    private fun findWifiToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // Enhanced WiFi keywords for different manufacturers
        val wifiKeywords = listOf(
            "wifi", "wi-fi", "wireless", "wlan", "wi fi",
            "wireless network", "wireless networks", "network",
            "internet", "connection", "connectivity"
        )

        Log.d(TAG, "🔍 Starting WiFi toggle search...")
        val result = findToggleByKeywords(node, wifiKeywords)

        if (result == null) {
            Log.d(TAG, "⚠️ WiFi toggle not found with keywords, trying alternative methods...")
            // Try to find any toggle in WiFi settings
            return findAnyToggleInWifiSettings(node)
        }

        return result
    }

    /**
     * Find any toggle in WiFi settings (fallback method)
     */
    private fun findAnyToggleInWifiSettings(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        Log.d(TAG, "🔍 Looking for any toggle in WiFi settings...")

        // Look for the first toggle/switch we can find
        val allToggles = findAllToggles(node)

        if (allToggles.isNotEmpty()) {
            Log.d(TAG, "✅ Found ${allToggles.size} toggles in WiFi settings")
            // Return the first toggle (usually the main WiFi toggle)
            return allToggles.first()
        }

        Log.d(TAG, "❌ No toggles found in WiFi settings")
        return null
    }

    /**
     * Find all toggle widgets in a node
     */
    private fun findAllToggles(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val toggles = mutableListOf<AccessibilityNodeInfo>()

        if (isToggleWidget(node)) {
            toggles.add(node)
            Log.d(TAG, "🔍 Found toggle: ${node.className}, text: '${node.text}', desc: '${node.contentDescription}'")
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                toggles.addAll(findAllToggles(child))
                child.recycle()
            }
        }

        return toggles
    }

    private fun toggleBluetooth(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingBluetoothToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open Bluetooth settings
            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened Bluetooth settings, pending toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle Bluetooth", e)
            false
        }
    }

    /**
     * Find Bluetooth toggle in settings
     */
    private fun findBluetoothToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // Look for switch with Bluetooth-related text
        val bluetoothKeywords = listOf("bluetooth", "bt", "blue tooth")

        return findToggleByKeywords(node, bluetoothKeywords)
    }

    /**
     * Find Airplane Mode toggle in settings
     */
    private fun findAirplaneModeToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val airplaneModeKeywords = listOf("airplane mode", "flight mode", "airplane", "flight")
        return findToggleByKeywords(node, airplaneModeKeywords)
    }

    /**
     * Find Mobile Data toggle in settings
     */
    private fun findMobileDataToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val mobileDataKeywords = listOf("mobile data", "cellular data", "data", "mobile network", "cellular")
        return findToggleByKeywords(node, mobileDataKeywords)
    }

    /**
     * Find Location toggle in settings
     */
    private fun findLocationToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val locationKeywords = listOf("location", "gps", "location services", "location access")
        return findToggleByKeywords(node, locationKeywords)
    }

    /**
     * Find Auto Rotate toggle in settings
     */
    private fun findAutoRotateToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val autoRotateKeywords = listOf("auto rotate", "auto-rotate", "rotation", "screen rotation", "rotate screen")
        return findToggleByKeywords(node, autoRotateKeywords)
    }

    /**
     * Find Do Not Disturb toggle in settings
     */
    private fun findDndToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val dndKeywords = listOf("do not disturb", "dnd", "silent mode", "quiet mode")
        return findToggleByKeywords(node, dndKeywords)
    }

    /**
     * Clear all pending toggles
     */
    private fun clearAllPendingToggles() {
        pendingWifiToggle = null
        pendingBluetoothToggle = null
        pendingAirplaneModeToggle = null
        pendingMobileDataToggle = null
        pendingLocationToggle = null
        pendingAutoRotateToggle = null
        pendingDndToggle = null
    }

    /**
     * Check if the package is a settings app
     */
    private fun isSettingsApp(packageName: String): Boolean {
        return packageName.contains("settings") ||
               packageName.contains("systemui") ||
               packageName == "com.android.settings" ||
               packageName == "com.samsung.android.settings" ||
               packageName == "com.oneplus.settings" ||
               packageName == "com.miui.securitycenter" ||
               packageName == "com.huawei.systemmanager"
    }

    private fun adjustVolume(direction: String): Boolean {
        return try {
            // Volume control through accessibility service is limited
            // This is a placeholder implementation
            when (direction.lowercase()) {
                "up", "down" -> {
                    // Could use AudioManager here instead
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to adjust volume", e)
            false
        }
    }

    private fun setBrightness(level: Int): Boolean {
        return try {
            // Open display settings
            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
            
            showToast("Please adjust brightness in settings")
            Log.d(TAG, "Opened display settings for brightness adjustment")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set brightness", e)
            false
        }
    }

    private fun toggleAirplaneMode(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingAirplaneModeToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Try main settings first (airplane mode is usually in main settings)
            val intent = Intent(Settings.ACTION_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened main settings, pending airplane mode toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle airplane mode", e)
            false
        }
    }

    private fun toggleMobileData(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingMobileDataToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open mobile network settings
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened mobile data settings, pending toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle mobile data", e)
            false
        }
    }

    private fun toggleLocation(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingLocationToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open location settings
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened location settings, pending toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle location", e)
            false
        }
    }

    private fun toggleAutoRotate(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingAutoRotateToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open display settings
            val intent = Intent(Settings.ACTION_DISPLAY_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened display settings, pending auto rotate toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle auto rotate", e)
            false
        }
    }

    private fun toggleDoNotDisturb(enable: Boolean): Boolean {
        return try {
            // Set pending toggle request
            pendingDndToggle = enable
            lastToggleRequestTime = System.currentTimeMillis()

            // Open sound settings (DND is usually here)
            val intent = Intent(Settings.ACTION_SOUND_SETTINGS).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)

            Log.d(TAG, "📱 Opened sound settings, pending DND toggle: ${if (enable) "ON" else "OFF"}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle do not disturb", e)
            false
        }
    }

    private fun takeScreenshot(): Boolean {
        return try {
            performGlobalAction(GLOBAL_ACTION_TAKE_SCREENSHOT)
            showToast("Screenshot taken")
            Log.d(TAG, "Screenshot taken")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to take screenshot", e)
            false
        }
    }

    private fun performClick(x: Float, y: Float): Boolean {
        return try {
            val path = Path().apply {
                moveTo(x, y)
            }
            
            val gesture = GestureDescription.Builder()
                .addStroke(GestureDescription.StrokeDescription(path, 0, 100))
                .build()
            
            dispatchGesture(gesture, null, null)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to perform click", e)
            false
        }
    }

    /**
     * Check for pending toggle requests and execute them
     */
    private fun checkForPendingToggles() {
        try {
            val currentTime = System.currentTimeMillis()

            // Only process toggles within 10 seconds of request
            if (currentTime - lastToggleRequestTime > 10000) {
                clearAllPendingToggles()
                return
            }

            val rootNode = rootInActiveWindow ?: return

            Log.d(TAG, "🔍 Checking for pending toggles in current window...")
            logCurrentWindowInfo(rootNode)

            // Check for WiFi toggle
            pendingWifiToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for WiFi toggle to set to: ${if (enable) "ON" else "OFF"}")
                val wifiToggle = findWifiToggleEnhanced(rootNode)
                if (wifiToggle != null) {
                    val isCurrentlyEnabled = wifiToggle.isChecked
                    Log.d(TAG, "📊 WiFi toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")
                    Log.d(TAG, "📊 Toggle details - Class: ${wifiToggle.className}, Text: '${wifiToggle.text}', Desc: '${wifiToggle.contentDescription}'")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking WiFi toggle...")
                        val clickSuccess = wifiToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        Log.d(TAG, "🎯 Click result: $clickSuccess")

                        if (clickSuccess) {
                            showToast("WiFi ${if (enable) "enabled" else "disabled"}")
                            pendingWifiToggle = null
                        } else {
                            Log.d(TAG, "⚠️ Click failed, trying alternative click methods...")
                            tryAlternativeClick(wifiToggle, enable)
                        }
                    } else {
                        Log.d(TAG, "✅ WiFi already in desired state")
                        showToast("WiFi is already ${if (enable) "enabled" else "disabled"}")
                        pendingWifiToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ WiFi toggle not found, will retry...")
                    // Try to find ANY toggle and click it (aggressive approach)
                    tryAggressiveWifiToggle(rootNode, enable)
                }
            }

            // Check for Bluetooth toggle
            pendingBluetoothToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Bluetooth toggle to set to: ${if (enable) "ON" else "OFF"}")
                val bluetoothToggle = findBluetoothToggle(rootNode)
                if (bluetoothToggle != null) {
                    val isCurrentlyEnabled = bluetoothToggle.isChecked
                    Log.d(TAG, "📊 Bluetooth toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Bluetooth toggle...")
                        bluetoothToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Bluetooth ${if (enable) "enabled" else "disabled"}")
                        pendingBluetoothToggle = null
                    } else {
                        Log.d(TAG, "✅ Bluetooth already in desired state")
                        showToast("Bluetooth is already ${if (enable) "enabled" else "disabled"}")
                        pendingBluetoothToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Bluetooth toggle not found, will retry...")
                }
            }

            // Check for Airplane Mode toggle
            pendingAirplaneModeToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Airplane Mode toggle to set to: ${if (enable) "ON" else "OFF"}")
                val airplaneModeToggle = findAirplaneModeToggle(rootNode)
                if (airplaneModeToggle != null) {
                    val isCurrentlyEnabled = airplaneModeToggle.isChecked
                    Log.d(TAG, "📊 Airplane Mode toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Airplane Mode toggle...")
                        airplaneModeToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Airplane Mode ${if (enable) "enabled" else "disabled"}")
                        pendingAirplaneModeToggle = null
                    } else {
                        Log.d(TAG, "✅ Airplane Mode already in desired state")
                        showToast("Airplane Mode is already ${if (enable) "enabled" else "disabled"}")
                        pendingAirplaneModeToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Airplane Mode toggle not found, will retry...")
                }
            }

            // Check for Mobile Data toggle
            pendingMobileDataToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Mobile Data toggle to set to: ${if (enable) "ON" else "OFF"}")
                val mobileDataToggle = findMobileDataToggle(rootNode)
                if (mobileDataToggle != null) {
                    val isCurrentlyEnabled = mobileDataToggle.isChecked
                    Log.d(TAG, "📊 Mobile Data toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Mobile Data toggle...")
                        mobileDataToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Mobile Data ${if (enable) "enabled" else "disabled"}")
                        pendingMobileDataToggle = null
                    } else {
                        Log.d(TAG, "✅ Mobile Data already in desired state")
                        showToast("Mobile Data is already ${if (enable) "enabled" else "disabled"}")
                        pendingMobileDataToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Mobile Data toggle not found, will retry...")
                }
            }

            // Check for Location toggle
            pendingLocationToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Location toggle to set to: ${if (enable) "ON" else "OFF"}")
                val locationToggle = findLocationToggle(rootNode)
                if (locationToggle != null) {
                    val isCurrentlyEnabled = locationToggle.isChecked
                    Log.d(TAG, "📊 Location toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Location toggle...")
                        locationToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Location ${if (enable) "enabled" else "disabled"}")
                        pendingLocationToggle = null
                    } else {
                        Log.d(TAG, "✅ Location already in desired state")
                        showToast("Location is already ${if (enable) "enabled" else "disabled"}")
                        pendingLocationToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Location toggle not found, will retry...")
                }
            }

            // Check for Auto Rotate toggle
            pendingAutoRotateToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Auto Rotate toggle to set to: ${if (enable) "ON" else "OFF"}")
                val autoRotateToggle = findAutoRotateToggle(rootNode)
                if (autoRotateToggle != null) {
                    val isCurrentlyEnabled = autoRotateToggle.isChecked
                    Log.d(TAG, "📊 Auto Rotate toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Auto Rotate toggle...")
                        autoRotateToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Auto Rotate ${if (enable) "enabled" else "disabled"}")
                        pendingAutoRotateToggle = null
                    } else {
                        Log.d(TAG, "✅ Auto Rotate already in desired state")
                        showToast("Auto Rotate is already ${if (enable) "enabled" else "disabled"}")
                        pendingAutoRotateToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Auto Rotate toggle not found, will retry...")
                }
            }

            // Check for Do Not Disturb toggle
            pendingDndToggle?.let { enable ->
                Log.d(TAG, "🔍 Looking for Do Not Disturb toggle to set to: ${if (enable) "ON" else "OFF"}")
                val dndToggle = findDndToggle(rootNode)
                if (dndToggle != null) {
                    val isCurrentlyEnabled = dndToggle.isChecked
                    Log.d(TAG, "📊 Do Not Disturb toggle found - Current: ${if (isCurrentlyEnabled) "ON" else "OFF"}, Target: ${if (enable) "ON" else "OFF"}")

                    if (isCurrentlyEnabled != enable) {
                        Log.d(TAG, "🎯 Clicking Do Not Disturb toggle...")
                        dndToggle.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                        showToast("Do Not Disturb ${if (enable) "enabled" else "disabled"}")
                        pendingDndToggle = null
                    } else {
                        Log.d(TAG, "✅ Do Not Disturb already in desired state")
                        showToast("Do Not Disturb is already ${if (enable) "enabled" else "disabled"}")
                        pendingDndToggle = null
                    }
                } else {
                    Log.d(TAG, "⚠️ Do Not Disturb toggle not found, will retry...")
                }
            }

            rootNode.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error checking for pending toggles", e)
        }
    }

    /**
     * Generic method to find toggle switches by keywords
     */
    private fun findToggleByKeywords(node: AccessibilityNodeInfo, keywords: List<String>): AccessibilityNodeInfo? {
        Log.d(TAG, "🔍 Searching for toggle with keywords: $keywords")

        // First, try to find a switch directly
        if (isToggleWidget(node)) {
            val nodeText = node.text?.toString()?.lowercase() ?: ""
            val nodeDesc = node.contentDescription?.toString()?.lowercase() ?: ""

            Log.d(TAG, "🔍 Found toggle widget - Text: '$nodeText', Desc: '$nodeDesc'")

            if (keywords.any { keyword ->
                nodeText.contains(keyword) || nodeDesc.contains(keyword)
            }) {
                Log.d(TAG, "✅ Toggle widget matches keywords!")
                return node
            }
        }

        // Look for text nodes that contain keywords and find nearby switches
        val textNodes = findTextNodesWithKeywords(node, keywords)
        for (textNode in textNodes) {
            Log.d(TAG, "🔍 Found text node with keyword: '${textNode.text}'")
            val nearbySwitch = findNearbySwitchWidget(textNode)
            if (nearbySwitch != null) {
                Log.d(TAG, "✅ Found nearby switch widget!")
                return nearbySwitch
            }
        }

        // Recursively search children
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findToggleByKeywords(child, keywords)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }

        return null
    }

    /**
     * Check if a node is a toggle widget
     */
    private fun isToggleWidget(node: AccessibilityNodeInfo): Boolean {
        val className = node.className?.toString() ?: ""
        return className.contains("Switch") ||
               className.contains("ToggleButton") ||
               className.contains("CheckBox") ||
               className.contains("CompoundButton") ||
               className.contains("RadioButton") ||
               (node.isCheckable && node.isClickable) ||
               (node.isClickable && (node.text?.toString()?.lowercase()?.contains("on") == true ||
                                   node.text?.toString()?.lowercase()?.contains("off") == true))
    }

    /**
     * Find text nodes that contain keywords
     */
    private fun findTextNodesWithKeywords(node: AccessibilityNodeInfo, keywords: List<String>): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()

        val nodeText = node.text?.toString()?.lowercase() ?: ""
        val nodeDesc = node.contentDescription?.toString()?.lowercase() ?: ""

        if (keywords.any { keyword ->
            nodeText.contains(keyword) || nodeDesc.contains(keyword)
        }) {
            result.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                result.addAll(findTextNodesWithKeywords(child, keywords))
                child.recycle()
            }
        }

        return result
    }

    /**
     * Find a switch widget near a text node
     */
    private fun findNearbySwitchWidget(textNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // Check parent and siblings
        val parent = textNode.parent
        if (parent != null) {
            // Check parent itself
            if (isToggleWidget(parent)) {
                return parent
            }

            // Check siblings
            for (i in 0 until parent.childCount) {
                val sibling = parent.getChild(i)
                if (sibling != null) {
                    if (isToggleWidget(sibling)) {
                        sibling.recycle()
                        return sibling
                    }

                    // Check sibling's children
                    val switchInSibling = findSwitchInNode(sibling)
                    if (switchInSibling != null) {
                        sibling.recycle()
                        return switchInSibling
                    }
                    sibling.recycle()
                }
            }
            parent.recycle()
        }

        return null
    }

    /**
     * Find switch widget in a node
     */
    private fun findSwitchInNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (isToggleWidget(node)) {
            return node
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findSwitchInNode(child)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }

        return null
    }

    /**
     * Log current window information for debugging
     */
    private fun logCurrentWindowInfo(rootNode: AccessibilityNodeInfo) {
        try {
            Log.d(TAG, "🔍 Current window package: ${rootNode.packageName}")
            Log.d(TAG, "🔍 Current window class: ${rootNode.className}")
            Log.d(TAG, "🔍 Window text: '${rootNode.text}'")
            Log.d(TAG, "🔍 Window description: '${rootNode.contentDescription}'")
            Log.d(TAG, "🔍 Child count: ${rootNode.childCount}")

            // Log all clickable/checkable elements
            val clickableElements = findAllClickableElements(rootNode)
            Log.d(TAG, "🔍 Found ${clickableElements.size} clickable elements")

            clickableElements.take(5).forEachIndexed { index, element ->
                Log.d(TAG, "🔍 Clickable[$index]: ${element.className}, text: '${element.text}', desc: '${element.contentDescription}', checkable: ${element.isCheckable}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error logging window info", e)
        }
    }

    /**
     * Find all clickable elements in the window
     */
    private fun findAllClickableElements(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val elements = mutableListOf<AccessibilityNodeInfo>()

        if (node.isClickable || node.isCheckable) {
            elements.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                elements.addAll(findAllClickableElements(child))
                child.recycle()
            }
        }

        return elements
    }

    /**
     * Enhanced WiFi toggle detection with multiple strategies
     */
    private fun findWifiToggleEnhanced(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        Log.d(TAG, "🔍 Enhanced WiFi toggle search starting...")

        // Strategy 1: Infinix/XOS specific detection
        var result = findInfinixWifiToggle(node)
        if (result != null) {
            Log.d(TAG, "✅ Found WiFi toggle via Infinix-specific method")
            return result
        }

        // Strategy 2: Look for WiFi-specific keywords
        val wifiKeywords = listOf("wifi", "wi-fi", "wireless", "wlan", "wi fi", "network", "internet")
        result = findToggleByKeywords(node, wifiKeywords)
        if (result != null) {
            Log.d(TAG, "✅ Found WiFi toggle via keywords")
            return result
        }

        // Strategy 3: Look for the first toggle in WiFi settings (usually the main toggle)
        val allToggles = findAllToggles(node)
        if (allToggles.isNotEmpty()) {
            Log.d(TAG, "✅ Found ${allToggles.size} toggles, using first one as WiFi toggle")
            return allToggles.first()
        }

        // Strategy 4: Look for any switch/toggle near the top of the screen
        val topToggles = findTogglesInTopArea(node)
        if (topToggles.isNotEmpty()) {
            Log.d(TAG, "✅ Found toggle in top area, assuming it's WiFi")
            return topToggles.first()
        }

        Log.d(TAG, "❌ No WiFi toggle found with any strategy")
        return null
    }

    /**
     * Infinix/XOS specific WiFi toggle detection
     */
    private fun findInfinixWifiToggle(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        Log.d(TAG, "🔍 Searching for Infinix/XOS WiFi toggle...")

        // Infinix often uses specific UI patterns
        val infinixPatterns = listOf(
            "com.android.settings:id/switch_widget",
            "com.android.settings:id/switchWidget",
            "android:id/switch_widget",
            "android:id/switchWidget"
        )

        // Look for switches with these resource IDs
        for (pattern in infinixPatterns) {
            val toggle = findToggleByResourceId(node, pattern)
            if (toggle != null) {
                Log.d(TAG, "✅ Found Infinix toggle with resource ID: $pattern")
                return toggle
            }
        }

        // Look for switches in specific Infinix UI containers
        val infinixContainers = listOf("LinearLayout", "RelativeLayout", "FrameLayout")
        for (container in infinixContainers) {
            val toggle = findToggleInContainer(node, container)
            if (toggle != null) {
                Log.d(TAG, "✅ Found toggle in Infinix container: $container")
                return toggle
            }
        }

        return null
    }

    /**
     * Find toggle by resource ID
     */
    private fun findToggleByResourceId(node: AccessibilityNodeInfo, resourceId: String): AccessibilityNodeInfo? {
        if (node.viewIdResourceName == resourceId && isToggleWidget(node)) {
            return node
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findToggleByResourceId(child, resourceId)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }

        return null
    }

    /**
     * Find toggle in specific container type
     */
    private fun findToggleInContainer(node: AccessibilityNodeInfo, containerType: String): AccessibilityNodeInfo? {
        if (node.className?.toString()?.contains(containerType) == true) {
            // Look for toggles in this container
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null && isToggleWidget(child)) {
                    child.recycle()
                    return child
                }
                child?.recycle()
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val result = findToggleInContainer(child, containerType)
                if (result != null) {
                    child.recycle()
                    return result
                }
                child.recycle()
            }
        }

        return null
    }

    /**
     * Find toggles in the top area of the screen
     */
    private fun findTogglesInTopArea(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val toggles = mutableListOf<AccessibilityNodeInfo>()

        // Get screen bounds
        val bounds = android.graphics.Rect()
        node.getBoundsInScreen(bounds)

        findTogglesInArea(node, bounds.height() / 3, toggles) // Top third of screen

        return toggles
    }

    /**
     * Find toggles within a specific area
     */
    private fun findTogglesInArea(node: AccessibilityNodeInfo, maxY: Int, toggles: MutableList<AccessibilityNodeInfo>) {
        if (isToggleWidget(node)) {
            val bounds = android.graphics.Rect()
            node.getBoundsInScreen(bounds)
            if (bounds.top < maxY) {
                toggles.add(node)
            }
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findTogglesInArea(child, maxY, toggles)
                child.recycle()
            }
        }
    }

    /**
     * Try alternative click methods when standard click fails
     */
    private fun tryAlternativeClick(toggle: AccessibilityNodeInfo, enable: Boolean) {
        try {
            // Method 1: Try clicking parent
            val parent = toggle.parent
            if (parent != null && parent.isClickable) {
                Log.d(TAG, "🎯 Trying to click parent element...")
                val success = parent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                if (success) {
                    Log.d(TAG, "✅ Parent click successful")
                    showToast("WiFi ${if (enable) "enabled" else "disabled"}")
                    pendingWifiToggle = null
                    parent.recycle()
                    return
                }
                parent.recycle()
            }

            // Method 2: Try gesture click at toggle position
            val bounds = android.graphics.Rect()
            toggle.getBoundsInScreen(bounds)
            val centerX = bounds.centerX().toFloat()
            val centerY = bounds.centerY().toFloat()

            Log.d(TAG, "🎯 Trying gesture click at ($centerX, $centerY)...")
            if (performClick(centerX, centerY)) {
                Log.d(TAG, "✅ Gesture click successful")
                showToast("WiFi ${if (enable) "enabled" else "disabled"}")
                pendingWifiToggle = null
                return
            }

            Log.d(TAG, "❌ All alternative click methods failed")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in alternative click", e)
        }
    }

    /**
     * Aggressive WiFi toggle - click any toggle found
     */
    private fun tryAggressiveWifiToggle(rootNode: AccessibilityNodeInfo, enable: Boolean) {
        try {
            Log.d(TAG, "🔥 Aggressive WiFi toggle mode - looking for ANY toggle...")

            val allClickableElements = findAllClickableElements(rootNode)
            Log.d(TAG, "🔍 Found ${allClickableElements.size} clickable elements")

            // Look for elements that might be toggles
            for (element in allClickableElements) {
                if (element.isCheckable ||
                    element.className?.toString()?.contains("Switch") == true ||
                    element.className?.toString()?.contains("Toggle") == true ||
                    element.text?.toString()?.lowercase()?.contains("on") == true ||
                    element.text?.toString()?.lowercase()?.contains("off") == true) {

                    Log.d(TAG, "🎯 Found potential toggle: ${element.className}, text: '${element.text}'")
                    Log.d(TAG, "🎯 Attempting aggressive click...")

                    val success = element.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    if (success) {
                        Log.d(TAG, "✅ Aggressive toggle click successful!")
                        showToast("WiFi toggle attempted")
                        pendingWifiToggle = null
                        return
                    }
                }
            }

            Log.d(TAG, "❌ Aggressive toggle failed - no suitable elements found")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in aggressive toggle", e)
        }
    }

    private fun showToast(message: String) {
        serviceScope.launch {
            Toast.makeText(this@ZaraAccessibilityService, message, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isServiceReady = false
        Log.d(TAG, "Zara Accessibility Service destroyed")
    }
}
