package com.zara.assistant.services

import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.net.Uri
import android.util.Log
import android.view.KeyEvent
import com.zara.assistant.domain.model.ActionType
import com.zara.assistant.domain.model.CommandType
import com.zara.assistant.domain.model.SystemAction
import com.zara.assistant.domain.model.VoiceCommand
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.regex.Pattern
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Local command processor for handling system commands without API calls
 * Uses advanced pattern matching and natural language understanding
 */
@Singleton
class LocalCommandProcessor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "LocalCommandProcessor"
    }

    /**
     * Process a voice command locally and determine if it can be handled without AI API
     */
    fun processCommand(command: VoiceCommand): LocalCommandResult {
        val text = command.text.lowercase().trim()

        // Try to match system control commands
        val systemAction = matchSystemCommand(text)
        if (systemAction != null) {
            return LocalCommandResult.SystemControl(
                action = systemAction,
                response = generateSystemResponse(systemAction),
                confidence = calculateConfidence(text, systemAction)
            )
        }
        
        // Try to match app control commands
        val appAction = matchAppCommand(text)
        if (appAction != null) {
            return LocalCommandResult.AppControl(
                action = appAction,
                response = generateAppResponse(appAction),
                confidence = calculateConfidence(text, appAction)
            )
        }
        
        // Try to match quick responses
        val quickResponse = matchQuickResponse(text)
        if (quickResponse != null) {
            return LocalCommandResult.QuickResponse(
                response = quickResponse,
                confidence = 0.9f
            )
        }
        
        // Command requires AI processing
        return LocalCommandResult.RequiresAI(
            commandType = classifyCommandType(text),
            reason = "Complex query requiring AI processing"
        )
    }

    /**
     * Match system control commands using advanced pattern matching
     */
    private fun matchSystemCommand(text: String): SystemAction? {
        // WiFi controls
        if (matchesPattern(text, WIFI_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_WIFI, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, WIFI_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_WIFI, parameters = mapOf("state" to "off"))
        }
        
        // Bluetooth controls
        if (matchesPattern(text, BLUETOOTH_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_BLUETOOTH, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, BLUETOOTH_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_BLUETOOTH, parameters = mapOf("state" to "off"))
        }
        
        // Volume controls
        val volumeMatch = matchVolumeCommand(text)
        if (volumeMatch != null) return volumeMatch
        
        // Brightness controls
        val brightnessMatch = matchBrightnessCommand(text)
        if (brightnessMatch != null) return brightnessMatch
        
        // Flashlight
        if (matchesPattern(text, FLASHLIGHT_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_FLASHLIGHT, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, FLASHLIGHT_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_FLASHLIGHT, parameters = mapOf("state" to "off"))
        }
        
        // Additional system controls
        if (matchesPattern(text, AIRPLANE_MODE_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_AIRPLANE_MODE, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, AIRPLANE_MODE_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_AIRPLANE_MODE, parameters = mapOf("state" to "off"))
        }

        if (matchesPattern(text, MOBILE_DATA_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_MOBILE_DATA, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, MOBILE_DATA_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_MOBILE_DATA, parameters = mapOf("state" to "off"))
        }

        if (matchesPattern(text, HOTSPOT_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_HOTSPOT, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, HOTSPOT_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_HOTSPOT, parameters = mapOf("state" to "off"))
        }

        if (matchesPattern(text, LOCATION_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_LOCATION, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, LOCATION_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_LOCATION, parameters = mapOf("state" to "off"))
        }

        if (matchesPattern(text, AUTO_ROTATE_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_AUTO_ROTATE, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, AUTO_ROTATE_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_AUTO_ROTATE, parameters = mapOf("state" to "off"))
        }

        if (matchesPattern(text, DND_ON_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_DO_NOT_DISTURB, parameters = mapOf("state" to "on"))
        }
        if (matchesPattern(text, DND_OFF_PATTERNS)) {
            return SystemAction(ActionType.TOGGLE_DO_NOT_DISTURB, parameters = mapOf("state" to "off"))
        }

        // Screenshot
        if (matchesPattern(text, SCREENSHOT_PATTERNS)) {
            return SystemAction(ActionType.TAKE_SCREENSHOT)
        }

        // Navigation
        if (matchesPattern(text, HOME_PATTERNS)) {
            return SystemAction(ActionType.GO_HOME)
        }
        if (matchesPattern(text, BACK_PATTERNS)) {
            return SystemAction(ActionType.GO_BACK)
        }
        if (matchesPattern(text, RECENT_APPS_PATTERNS)) {
            return SystemAction(ActionType.OPEN_RECENT_APPS)
        }
        
        return null
    }

    /**
     * Match app control commands
     */
    private fun matchAppCommand(text: String): SystemAction? {
        // Open app patterns
        val openMatch = extractAppFromOpenCommand(text)
        if (openMatch != null) {
            return SystemAction(ActionType.OPEN_APP, target = openMatch)
        }
        
        // Close app patterns
        if (matchesPattern(text, CLOSE_APP_PATTERNS)) {
            return SystemAction(ActionType.CLOSE_APP)
        }
        
        // Switch app patterns
        val switchMatch = extractAppFromSwitchCommand(text)
        if (switchMatch != null) {
            return SystemAction(ActionType.SWITCH_APP, target = switchMatch)
        }
        
        return null
    }

    /**
     * Match quick response patterns
     */
    private fun matchQuickResponse(text: String): String? {
        return when {
            matchesPattern(text, GREETING_PATTERNS) -> "Hello! How can I help you today?"
            matchesPattern(text, THANKS_PATTERNS) -> "You're welcome! Is there anything else I can help you with?"
            matchesPattern(text, GOODBYE_PATTERNS) -> "Goodbye! Have a great day!"
            matchesPattern(text, HOW_ARE_YOU_PATTERNS) -> "I'm doing well, thank you for asking! How can I assist you?"
            matchesPattern(text, WHAT_TIME_PATTERNS) -> getCurrentTimeResponse()
            else -> null
        }
    }

    /**
     * Advanced pattern matching with fuzzy matching and synonyms
     */
    private fun matchesPattern(text: String, patterns: List<String>): Boolean {
        return patterns.any { pattern ->
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            regex.matcher(text).find()
        }
    }

    /**
     * Extract app name from open command
     */
    private fun extractAppFromOpenCommand(text: String): String? {
        // Clean the text first
        val cleanText = text.lowercase().trim()

        // Enhanced patterns that handle multiple commands and extract only the first app
        val openPatterns = listOf(
            "(?:open|launch|start|run)\\s+([a-zA-Z\\s]+?)(?:\\s+(?:sir|please|app))?(?:\\s+(?:open|launch|start|run).*)?$",
            "(?:open|launch|start|run)\\s+([a-zA-Z\\s]+?)(?:\\s+app)?$",
            "(?:open|launch|start|run)\\s+(\\w+(?:\\s+\\w+)*?)(?:\\s|$)"
        )

        for (pattern in openPatterns) {
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(cleanText)
            if (matcher.find()) {
                val appName = matcher.group(1)?.trim()
                if (!appName.isNullOrBlank()) {
                    // Clean up common words that shouldn't be part of app name
                    val cleanedAppName = appName
                        .replace("\\s+sir\\s*".toRegex(), " ")
                        .replace("\\s+please\\s*".toRegex(), " ")
                        .replace("\\s+app\\s*$".toRegex(), "")
                        .trim()

                    if (cleanedAppName.isNotBlank()) {
                        return cleanedAppName
                    }
                }
            }
        }
        return null
    }

    /**
     * Extract app name from switch command
     */
    private fun extractAppFromSwitchCommand(text: String): String? {
        val switchPatterns = listOf(
            "switch\\s+to\\s+(\\w+)",
            "go\\s+to\\s+(\\w+)",
            "change\\s+to\\s+(\\w+)"
        )
        
        for (pattern in switchPatterns) {
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(text)
            if (matcher.find()) {
                return matcher.group(1)
            }
        }
        return null
    }

    /**
     * Match volume commands with level extraction
     */
    private fun matchVolumeCommand(text: String): SystemAction? {
        // Volume up/down
        if (matchesPattern(text, VOLUME_UP_PATTERNS)) {
            return SystemAction(ActionType.ADJUST_VOLUME, parameters = mapOf("direction" to "up"))
        }
        if (matchesPattern(text, VOLUME_DOWN_PATTERNS)) {
            return SystemAction(ActionType.ADJUST_VOLUME, parameters = mapOf("direction" to "down"))
        }
        
        // Set volume to specific level
        val levelMatch = extractVolumeLevel(text)
        if (levelMatch != null) {
            return SystemAction(ActionType.SET_VOLUME, parameters = mapOf("level" to levelMatch.toString()))
        }
        
        // Mute
        if (matchesPattern(text, MUTE_PATTERNS)) {
            return SystemAction(ActionType.MUTE_VOLUME)
        }
        
        return null
    }

    /**
     * Match brightness commands with level extraction
     */
    private fun matchBrightnessCommand(text: String): SystemAction? {
        // Brightness up/down
        if (matchesPattern(text, BRIGHTNESS_UP_PATTERNS)) {
            return SystemAction(ActionType.ADJUST_BRIGHTNESS, parameters = mapOf("direction" to "up"))
        }
        if (matchesPattern(text, BRIGHTNESS_DOWN_PATTERNS)) {
            return SystemAction(ActionType.ADJUST_BRIGHTNESS, parameters = mapOf("direction" to "down"))
        }

        // Set brightness to specific level
        val levelMatch = extractBrightnessLevel(text)
        if (levelMatch != null) {
            return SystemAction(ActionType.SET_BRIGHTNESS, parameters = mapOf("level" to levelMatch.toString()))
        }

        return null
    }

    /**
     * Match music commands with song/artist extraction and execute Spotify actions
     */
    private fun matchMusicCommand(text: String): SystemAction? {
        // Play music commands
        if (matchesPattern(text, MUSIC_PLAY_PATTERNS)) {
            val songInfo = extractSongInfo(text)

            // Execute Spotify playback immediately
            playSpotifyMusic(songInfo)

            return SystemAction(
                ActionType.PLAY_MUSIC,
                parameters = songInfo
            )
        }

        // Pause music
        if (matchesPattern(text, MUSIC_PAUSE_PATTERNS)) {
            sendMediaKey(KeyEvent.KEYCODE_MEDIA_PAUSE)
            return SystemAction(ActionType.PAUSE_MUSIC)
        }

        // Stop music
        if (matchesPattern(text, MUSIC_STOP_PATTERNS)) {
            sendMediaKey(KeyEvent.KEYCODE_MEDIA_STOP)
            return SystemAction(ActionType.STOP_MUSIC)
        }

        // Next track
        if (matchesPattern(text, MUSIC_NEXT_PATTERNS)) {
            sendMediaKey(KeyEvent.KEYCODE_MEDIA_NEXT)
            return SystemAction(ActionType.NEXT_TRACK)
        }

        // Previous track
        if (matchesPattern(text, MUSIC_PREVIOUS_PATTERNS)) {
            sendMediaKey(KeyEvent.KEYCODE_MEDIA_PREVIOUS)
            return SystemAction(ActionType.PREVIOUS_TRACK)
        }

        return null
    }

    /**
     * Extract volume level from text (0-100)
     */
    private fun extractVolumeLevel(text: String): Int? {
        val patterns = listOf(
            "set\\s+volume\\s+to\\s+(\\d+)",
            "volume\\s+(\\d+)",
            "set\\s+volume\\s+(\\d+)"
        )
        
        for (pattern in patterns) {
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(text)
            if (matcher.find()) {
                val level = matcher.group(1).toIntOrNull()
                return if (level != null && level in 0..100) level else null
            }
        }
        return null
    }

    /**
     * Extract brightness level from text (0-100)
     */
    private fun extractBrightnessLevel(text: String): Int? {
        val patterns = listOf(
            "set\\s+brightness\\s+to\\s+(\\d+)",
            "brightness\\s+(\\d+)",
            "set\\s+brightness\\s+(\\d+)"
        )

        for (pattern in patterns) {
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(text)
            if (matcher.find()) {
                val level = matcher.group(1).toIntOrNull()
                return if (level != null && level in 0..100) level else null
            }
        }
        return null
    }

    /**
     * Extract song/artist information from music commands
     */
    private fun extractSongInfo(text: String): Map<String, String> {
        val params = mutableMapOf<String, String>()

        // Extract song name patterns
        val songPatterns = listOf(
            "play song (.+)",
            "play (.+) song",
            "play (.+) by (.+)",
            "play (.+)"
        )

        for (pattern in songPatterns) {
            val regex = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE)
            val matcher = regex.matcher(text)
            if (matcher.find()) {
                when (pattern) {
                    "play (.+) by (.+)" -> {
                        params["song"] = matcher.group(1)?.trim() ?: ""
                        params["artist"] = matcher.group(2)?.trim() ?: ""
                    }
                    "play song (.+)", "play (.+) song" -> {
                        params["song"] = matcher.group(1)?.trim() ?: ""
                    }
                    "play (.+)" -> {
                        val query = matcher.group(1)?.trim() ?: ""
                        // Check if it contains "by" to separate artist
                        if (query.contains(" by ")) {
                            val parts = query.split(" by ")
                            params["song"] = parts[0].trim()
                            params["artist"] = parts[1].trim()
                        } else {
                            params["query"] = query
                        }
                    }
                }
                break
            }
        }

        // If no specific song found, mark as general music request
        if (params.isEmpty()) {
            params["action"] = "play_general"
        }

        return params
    }

    /**
     * Generate appropriate response for system actions
     */
    private fun generateSystemResponse(action: SystemAction): String {
        return when (action.type) {
            ActionType.TOGGLE_WIFI -> {
                val state = action.parameters["state"]
                "Turning WiFi ${state ?: "on"}"
            }
            ActionType.TOGGLE_BLUETOOTH -> {
                val state = action.parameters["state"]
                "Turning Bluetooth ${state ?: "on"}"
            }
            ActionType.ADJUST_VOLUME -> {
                val direction = action.parameters["direction"]
                "Adjusting volume ${direction ?: "up"}"
            }
            ActionType.SET_VOLUME -> {
                val level = action.parameters["level"]
                "Setting volume to ${level ?: "50"}%"
            }
            ActionType.ADJUST_BRIGHTNESS -> {
                val direction = action.parameters["direction"]
                "Adjusting brightness ${direction ?: "up"}"
            }
            ActionType.SET_BRIGHTNESS -> {
                val level = action.parameters["level"]
                "Setting brightness to ${level ?: "50"}%"
            }
            ActionType.TOGGLE_FLASHLIGHT -> {
                val state = action.parameters["state"]
                "Turning flashlight ${state ?: "on"}"
            }
            ActionType.TOGGLE_AIRPLANE_MODE -> {
                val state = action.parameters["state"]
                "Turning airplane mode ${state ?: "on"}"
            }
            ActionType.TOGGLE_MOBILE_DATA -> {
                val state = action.parameters["state"]
                "Turning mobile data ${state ?: "on"}"
            }
            ActionType.TOGGLE_HOTSPOT -> {
                val state = action.parameters["state"]
                "Turning mobile hotspot ${state ?: "on"}"
            }
            ActionType.TOGGLE_LOCATION -> {
                val state = action.parameters["state"]
                "Turning location services ${state ?: "on"}"
            }
            ActionType.TOGGLE_AUTO_ROTATE -> {
                val state = action.parameters["state"]
                "Turning auto rotate ${state ?: "on"}"
            }
            ActionType.TOGGLE_DO_NOT_DISTURB -> {
                val state = action.parameters["state"]
                "Turning do not disturb ${state ?: "on"}"
            }
            ActionType.TAKE_SCREENSHOT -> "Taking screenshot"
            ActionType.GO_HOME -> "Going to home screen"
            ActionType.GO_BACK -> "Going back"
            ActionType.OPEN_RECENT_APPS -> "Opening recent apps"

            // Music control responses
            ActionType.PLAY_MUSIC -> {
                val song = action.parameters["song"]
                val artist = action.parameters["artist"]
                val query = action.parameters["query"]
                when {
                    !song.isNullOrEmpty() && !artist.isNullOrEmpty() -> "Playing $song by $artist on Spotify"
                    !song.isNullOrEmpty() -> "Playing $song on Spotify"
                    !query.isNullOrEmpty() -> "Playing $query on Spotify"
                    else -> "Starting music playback on Spotify"
                }
            }
            ActionType.PAUSE_MUSIC -> "Pausing music"
            ActionType.STOP_MUSIC -> "Stopping music"
            ActionType.NEXT_TRACK -> "Playing next track"
            ActionType.PREVIOUS_TRACK -> "Playing previous track"

            else -> "Executing ${action.type.name.lowercase().replace("_", " ")}"
        }
    }

    /**
     * Generate appropriate response for app actions
     */
    private fun generateAppResponse(action: SystemAction): String {
        return when (action.type) {
            ActionType.OPEN_APP -> "Opening ${action.target}"
            ActionType.CLOSE_APP -> "Closing current app"
            ActionType.SWITCH_APP -> "Switching to ${action.target}"
            else -> "Executing app command"
        }
    }

    /**
     * Calculate confidence score for command matching
     */
    private fun calculateConfidence(text: String, action: SystemAction): Float {
        // Simple confidence calculation based on pattern matching
        // In a real implementation, this could be more sophisticated
        return when {
            text.contains(action.type.name.lowercase().replace("_", " ")) -> 0.95f
            action.target.isNotEmpty() && text.contains(action.target.lowercase()) -> 0.9f
            else -> 0.8f
        }
    }

    /**
     * Classify command type for AI processing
     */
    private fun classifyCommandType(text: String): CommandType {
        return when {
            containsWeatherKeywords(text) -> CommandType.INFORMATION
            containsCallKeywords(text) -> CommandType.COMMUNICATION
            containsMusicKeywords(text) -> CommandType.ENTERTAINMENT
            else -> CommandType.CONVERSATION
        }
    }

    private fun containsWeatherKeywords(text: String): Boolean {
        val keywords = listOf("weather", "temperature", "rain", "sunny", "cloudy", "forecast")
        return keywords.any { text.contains(it) }
    }

    private fun containsCallKeywords(text: String): Boolean {
        val keywords = listOf("call", "phone", "dial", "message", "text", "sms")
        return keywords.any { text.contains(it) }
    }

    private fun containsMusicKeywords(text: String): Boolean {
        val keywords = listOf("music", "song", "play", "pause", "next", "previous", "spotify")
        return keywords.any { text.contains(it) }
    }

    private fun getCurrentTimeResponse(): String {
        val currentTime = java.text.SimpleDateFormat("h:mm a", java.util.Locale.getDefault())
            .format(java.util.Date())
        return "It's currently $currentTime"
    }

    // Pattern definitions
    private val WIFI_ON_PATTERNS = listOf(
        "turn on wifi", "enable wifi", "wifi on", "connect wifi", "turn wifi on",
        "turn on wi-fi", "enable wi-fi", "wi-fi on", "connect wi-fi", "turn wi-fi on",
        "turn on wireless", "enable wireless", "wireless on", "connect wireless"
    )

    private val WIFI_OFF_PATTERNS = listOf(
        "turn off wifi", "disable wifi", "wifi off", "disconnect wifi", "turn wifi off",
        "turn off wi-fi", "disable wi-fi", "wi-fi off", "disconnect wi-fi", "turn wi-fi off",
        "turn off wireless", "disable wireless", "wireless off", "disconnect wireless"
    )
    
    private val BLUETOOTH_ON_PATTERNS = listOf(
        "turn on bluetooth", "enable bluetooth", "bluetooth on", "turn bluetooth on",
        "turn on blue tooth", "enable blue tooth", "blue tooth on", "turn blue tooth on",
        "turn on bt", "enable bt", "bt on", "turn bt on"
    )

    private val BLUETOOTH_OFF_PATTERNS = listOf(
        "turn off bluetooth", "disable bluetooth", "bluetooth off", "turn bluetooth off",
        "turn off blue tooth", "disable blue tooth", "blue tooth off", "turn blue tooth off",
        "turn off bt", "disable bt", "bt off", "turn bt off"
    )
    
    private val VOLUME_UP_PATTERNS = listOf(
        "volume up", "increase volume", "turn up volume", "louder"
    )
    
    private val VOLUME_DOWN_PATTERNS = listOf(
        "volume down", "decrease volume", "turn down volume", "quieter", "lower volume"
    )
    
    private val MUTE_PATTERNS = listOf(
        "mute", "silence", "turn off sound", "mute volume"
    )
    
    private val BRIGHTNESS_UP_PATTERNS = listOf(
        "brightness up", "increase brightness", "brighter", "turn up brightness"
    )
    
    private val BRIGHTNESS_DOWN_PATTERNS = listOf(
        "brightness down", "decrease brightness", "dimmer", "turn down brightness"
    )

    // Music control patterns
    private val MUSIC_PLAY_PATTERNS = listOf(
        "play music", "start music", "play song", "play a song",
        "play (.+)", "play song (.+)", "play (.+) song",
        "play (.+) by (.+)", "start playing", "put on music",
        "turn on music", "music on", "start the music"
    )

    private val MUSIC_PAUSE_PATTERNS = listOf(
        "pause music", "pause song", "pause", "stop playing",
        "pause the music", "pause the song", "music pause"
    )

    private val MUSIC_STOP_PATTERNS = listOf(
        "stop music", "stop song", "stop", "turn off music",
        "stop the music", "stop the song", "music stop", "music off"
    )

    private val MUSIC_NEXT_PATTERNS = listOf(
        "next song", "next track", "skip song", "skip track",
        "next", "skip", "play next", "next music"
    )

    private val MUSIC_PREVIOUS_PATTERNS = listOf(
        "previous song", "previous track", "last song", "last track",
        "previous", "go back", "play previous", "previous music"
    )

    private val FLASHLIGHT_ON_PATTERNS = listOf(
        "turn on flashlight", "flashlight on", "turn on torch", "torch on"
    )

    private val FLASHLIGHT_OFF_PATTERNS = listOf(
        "turn off flashlight", "flashlight off", "turn off torch", "torch off"
    )
    
    private val SCREENSHOT_PATTERNS = listOf(
        "take screenshot", "screenshot", "capture screen", "take a picture of screen"
    )
    
    private val HOME_PATTERNS = listOf(
        "go home", "home screen", "go to home", "home"
    )
    
    private val BACK_PATTERNS = listOf(
        "go back", "back", "previous screen"
    )
    
    private val RECENT_APPS_PATTERNS = listOf(
        "recent apps", "open recent", "recent applications", "app switcher"
    )
    
    private val CLOSE_APP_PATTERNS = listOf(
        "close app", "close this", "exit app", "quit"
    )
    
    private val GREETING_PATTERNS = listOf(
        "hello", "hi", "hey", "good morning", "good afternoon", "good evening"
    )
    
    private val THANKS_PATTERNS = listOf(
        "thank you", "thanks", "appreciate it", "thank you very much"
    )
    
    private val GOODBYE_PATTERNS = listOf(
        "goodbye", "bye", "see you later", "farewell", "good night"
    )
    
    private val HOW_ARE_YOU_PATTERNS = listOf(
        "how are you", "how's it going", "how are things"
    )
    
    private val WHAT_TIME_PATTERNS = listOf(
        "what time", "current time", "time is it", "tell me the time"
    )

    // Additional system control patterns
    private val AIRPLANE_MODE_ON_PATTERNS = listOf(
        "turn on airplane mode", "enable airplane mode", "airplane mode on", "flight mode on"
    )

    private val AIRPLANE_MODE_OFF_PATTERNS = listOf(
        "turn off airplane mode", "disable airplane mode", "airplane mode off", "flight mode off"
    )

    private val MOBILE_DATA_ON_PATTERNS = listOf(
        "turn on mobile data", "enable mobile data", "mobile data on", "turn on data"
    )

    private val MOBILE_DATA_OFF_PATTERNS = listOf(
        "turn off mobile data", "disable mobile data", "mobile data off", "turn off data"
    )

    private val HOTSPOT_ON_PATTERNS = listOf(
        "turn on hotspot", "enable hotspot", "hotspot on", "turn on mobile hotspot"
    )

    private val HOTSPOT_OFF_PATTERNS = listOf(
        "turn off hotspot", "disable hotspot", "hotspot off", "turn off mobile hotspot"
    )

    private val LOCATION_ON_PATTERNS = listOf(
        "turn on location", "enable location", "location on", "turn on gps", "enable gps"
    )

    private val LOCATION_OFF_PATTERNS = listOf(
        "turn off location", "disable location", "location off", "turn off gps", "disable gps"
    )

    private val AUTO_ROTATE_ON_PATTERNS = listOf(
        "turn on auto rotate", "enable auto rotate", "auto rotate on", "enable rotation"
    )

    private val AUTO_ROTATE_OFF_PATTERNS = listOf(
        "turn off auto rotate", "disable auto rotate", "auto rotate off", "disable rotation"
    )

    private val DND_ON_PATTERNS = listOf(
        "turn on do not disturb", "enable do not disturb", "do not disturb on", "turn on dnd", "silent mode on"
    )

    private val DND_OFF_PATTERNS = listOf(
        "turn off do not disturb", "disable do not disturb", "do not disturb off", "turn off dnd", "silent mode off"
    )

    // ==================== SPOTIFY MUSIC CONTROL METHODS ====================

    /**
     * Play music on Spotify
     */
    private fun playSpotifyMusic(songInfo: Map<String, String>) {
        try {
            Log.d(TAG, "🎵 Playing music on Spotify: $songInfo")

            val song = songInfo["song"]
            val artist = songInfo["artist"]
            val query = songInfo["query"]

            // Build search query
            val searchQuery = when {
                !song.isNullOrEmpty() && !artist.isNullOrEmpty() -> "$song $artist"
                !song.isNullOrEmpty() -> song
                !query.isNullOrEmpty() -> query
                else -> "music"
            }

            // Try to open Spotify with search
            val intent = Intent(Intent.ACTION_SEARCH).apply {
                setPackage("com.spotify.music")
                putExtra("query", searchQuery)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
                Log.d(TAG, "✅ Started Spotify with search: $searchQuery")
            } else {
                // Fallback: just open Spotify
                val launchIntent = context.packageManager.getLaunchIntentForPackage("com.spotify.music")
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(launchIntent)
                    Log.d(TAG, "✅ Opened Spotify")
                } else {
                    Log.w(TAG, "⚠️ Spotify not installed, using media key")
                    sendMediaKey(KeyEvent.KEYCODE_MEDIA_PLAY)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error playing Spotify music", e)
            sendMediaKey(KeyEvent.KEYCODE_MEDIA_PLAY)
        }
    }

    /**
     * Send media key event for music control
     */
    private fun sendMediaKey(keyCode: Int) {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

            val downEvent = KeyEvent(KeyEvent.ACTION_DOWN, keyCode)
            val upEvent = KeyEvent(KeyEvent.ACTION_UP, keyCode)

            audioManager.dispatchMediaKeyEvent(downEvent)
            audioManager.dispatchMediaKeyEvent(upEvent)

            Log.d(TAG, "✅ Sent media key: $keyCode")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error sending media key", e)
        }
    }
}

/**
 * Result of local command processing
 */
sealed class LocalCommandResult {
    data class SystemControl(
        val action: SystemAction,
        val response: String,
        val confidence: Float
    ) : LocalCommandResult()
    
    data class AppControl(
        val action: SystemAction,
        val response: String,
        val confidence: Float
    ) : LocalCommandResult()
    
    data class QuickResponse(
        val response: String,
        val confidence: Float
    ) : LocalCommandResult()
    
    data class RequiresAI(
        val commandType: CommandType,
        val reason: String
    ) : LocalCommandResult()
}
