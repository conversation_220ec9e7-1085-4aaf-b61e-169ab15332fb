package com.zara.assistant.core

/**
 * Application-wide constants
 */
object Constants {
    
    // API Configuration
    object API {
        const val COHERE_BASE_URL = "https://api.cohere.ai/"
        const val PERPLEXITY_BASE_URL = "https://api.perplexity.ai/"
        const val REQUEST_TIMEOUT = 30L // seconds
        const val CONNECT_TIMEOUT = 15L // seconds
        const val READ_TIMEOUT = 30L // seconds
    }
    
    // Wake Word Configuration
    object WakeWord {
        const val KEYWORD = "Hey Zara"
        const val ALTERNATIVE_KEYWORD = "Zara"
        const val DEFAULT_SENSITIVITY = 0.5f
        const val MIN_SENSITIVITY = 0.1f
        const val MAX_SENSITIVITY = 1.0f
        const val AUDIO_SAMPLE_RATE = 16000
        const val FRAME_LENGTH = 512
    }
    
    // Voice Configuration
    object Voice {
        const val DEFAULT_SPEECH_RATE = 1.0f
        const val DEFAULT_PITCH = 1.0f
        const val MIN_SPEECH_RATE = 0.5f
        const val MAX_SPEECH_RATE = 2.0f
        const val MIN_PITCH = 0.5f
        const val MAX_PITCH = 2.0f
        const val RECOGNITION_TIMEOUT = 5000L // milliseconds
        const val LISTENING_TIMEOUT = 10000L // milliseconds
    }
    
    // AI Configuration
    object AI {
        const val MAX_TOKENS = 75 // Reduced for shorter voice responses
        const val TEMPERATURE = 0.7f
        const val TOP_P = 0.9f
        const val FREQUENCY_PENALTY = 0.0f
        const val PRESENCE_PENALTY = 0.0f
        const val MAX_CONVERSATION_HISTORY = 10
        const val RESPONSE_TIMEOUT = 15000L // milliseconds
    }
    
    // Database Configuration
    object Database {
        const val DATABASE_NAME = "zara_database"
        const val DATABASE_VERSION = 1
        const val CONVERSATION_TABLE = "conversations"
        const val SETTINGS_TABLE = "settings"
        const val COMMANDS_TABLE = "commands"
    }
    
    // Preferences Keys
    object Preferences {
        const val WAKE_WORD_ENABLED = "wake_word_enabled"
        const val WAKE_WORD_SENSITIVITY = "wake_word_sensitivity"
        const val SPEECH_RATE = "speech_rate"
        const val SPEECH_PITCH = "speech_pitch"
        const val AI_PERSONALITY = "ai_personality"
        const val AI_RESPONSE_STYLE = "ai_response_style"
        const val VOICE_LANGUAGE = "voice_language"
        const val AUTO_LISTEN = "auto_listen"
        const val CONVERSATION_HISTORY_ENABLED = "conversation_history_enabled"
        const val ANALYTICS_ENABLED = "analytics_enabled"
        const val FIRST_LAUNCH = "first_launch"
        const val ACCESSIBILITY_SERVICE_ENABLED = "accessibility_service_enabled"
        const val NOTIFICATION_ACCESS_ENABLED = "notification_access_enabled"
    }
    
    // Voice States
    enum class VoiceState {
        IDLE,
        LISTENING_WAKE_WORD,
        LISTENING_COMMAND,
        PROCESSING,
        SPEAKING,
        ERROR
    }
    
    // AI Personalities
    enum class AIPersonality(val displayName: String) {
        PROFESSIONAL("Professional"),
        FRIENDLY("Friendly"),
        CASUAL("Casual")
    }
    
    // AI Response Styles
    enum class AIResponseStyle(val displayName: String) {
        BRIEF("Brief"),
        DETAILED("Detailed"),
        CONVERSATIONAL("Conversational")
    }
    
    // System Commands
    object SystemCommands {
        const val OPEN_APP = "open"
        const val CLOSE_APP = "close"
        const val SWITCH_APP = "switch"
        const val TURN_ON_WIFI = "turn_on_wifi"
        const val TURN_OFF_WIFI = "turn_off_wifi"
        const val TURN_ON_BLUETOOTH = "turn_on_bluetooth"
        const val TURN_OFF_BLUETOOTH = "turn_off_bluetooth"
        const val INCREASE_VOLUME = "increase_volume"
        const val DECREASE_VOLUME = "decrease_volume"
        const val SET_BRIGHTNESS = "set_brightness"
        const val ENABLE_AIRPLANE_MODE = "enable_airplane_mode"
        const val DISABLE_AIRPLANE_MODE = "disable_airplane_mode"
        const val CALL_CONTACT = "call_contact"
        const val SEND_MESSAGE = "send_message"
        const val READ_NOTIFICATIONS = "read_notifications"
        const val PLAY_MUSIC = "play_music"
        const val PAUSE_MUSIC = "pause_music"
        const val NEXT_SONG = "next_song"
        const val PREVIOUS_SONG = "previous_song"
    }
    
    // Permissions
    object Permissions {
        const val RECORD_AUDIO = android.Manifest.permission.RECORD_AUDIO
        const val SYSTEM_ALERT_WINDOW = android.Manifest.permission.SYSTEM_ALERT_WINDOW
        const val CALL_PHONE = android.Manifest.permission.CALL_PHONE
        const val SEND_SMS = android.Manifest.permission.SEND_SMS
        const val READ_CONTACTS = android.Manifest.permission.READ_CONTACTS
        const val ACCESS_FINE_LOCATION = android.Manifest.permission.ACCESS_FINE_LOCATION
        const val ACCESS_COARSE_LOCATION = android.Manifest.permission.ACCESS_COARSE_LOCATION
        const val READ_EXTERNAL_STORAGE = android.Manifest.permission.READ_EXTERNAL_STORAGE
        const val WRITE_EXTERNAL_STORAGE = android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        const val POST_NOTIFICATIONS = android.Manifest.permission.POST_NOTIFICATIONS
    }
    
    // Intent Actions
    object IntentActions {
        const val VOICE_COMMAND = "com.zara.assistant.VOICE_COMMAND"
        const val WAKE_WORD_DETECTED = "com.zara.assistant.WAKE_WORD_DETECTED"
        const val START_LISTENING = "com.zara.assistant.START_LISTENING"
        const val STOP_LISTENING = "com.zara.assistant.STOP_LISTENING"
        const val PROCESS_COMMAND = "com.zara.assistant.PROCESS_COMMAND"
        const val SPEAK_RESPONSE = "com.zara.assistant.SPEAK_RESPONSE"
    }
    
    // Error Codes
    object ErrorCodes {
        const val MICROPHONE_PERMISSION_DENIED = 1001
        const val NETWORK_ERROR = 1002
        const val AI_SERVICE_ERROR = 1003
        const val SPEECH_RECOGNITION_ERROR = 1004
        const val TEXT_TO_SPEECH_ERROR = 1005
        const val ACCESSIBILITY_SERVICE_ERROR = 1006
        const val WAKE_WORD_SERVICE_ERROR = 1007
        const val UNKNOWN_ERROR = 9999
    }
    
    // Animation Durations
    object Animation {
        const val SHORT_DURATION = 150L
        const val MEDIUM_DURATION = 300L
        const val LONG_DURATION = 500L
        const val VOICE_RIPPLE_DURATION = 1000L
        const val FADE_DURATION = 200L
        const val SLIDE_DURATION = 250L
    }
    
    // UI Constants
    object UI {
        const val VOICE_BUTTON_SIZE_DP = 120
        const val CARD_CORNER_RADIUS_DP = 20
        const val BUTTON_CORNER_RADIUS_DP = 16
        const val ELEVATION_DP = 8
        const val MARGIN_DP = 16
        const val PADDING_DP = 16
    }
}
