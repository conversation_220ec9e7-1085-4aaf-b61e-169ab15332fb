package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.domain.model.AudioConfig
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for voice-related operations
 */
interface VoiceRepository {
    
    /**
     * Voice state management
     */
    fun getVoiceState(): Flow<VoiceState>
    suspend fun updateVoiceState(state: VoiceState)
    suspend fun setVoiceState(newState: VoiceState.State)
    
    /**
     * Wake word detection
     */
    suspend fun startWakeWordDetection(): Result<Unit>
    suspend fun stopWakeWordDetection(): Result<Unit>
    fun isWakeWordDetectionActive(): Flow<Boolean>
    
    /**
     * Speech recognition
     */
    suspend fun startListening(): Result<Unit>
    suspend fun stopListening(): Result<Unit>
    fun getRecognizedText(): Flow<String>
    suspend fun processVoiceCommand(audioData: ByteArray): Result<VoiceCommand>
    
    /**
     * Text-to-speech
     */
    suspend fun speak(text: String): Result<Unit>
    suspend fun stopSpeaking(): Result<Unit>
    fun isSpeaking(): Flow<Boolean>
    
    /**
     * Voice settings
     */
    suspend fun getVoiceSettings(): VoiceSettings
    suspend fun updateVoiceSettings(settings: VoiceSettings): Result<Unit>
    suspend fun getAudioConfig(): AudioConfig
    suspend fun updateAudioConfig(config: AudioConfig): Result<Unit>
    
    /**
     * Permissions
     */
    suspend fun checkMicrophonePermission(): Boolean
    suspend fun requestMicrophonePermission(): Result<Boolean>
    
    /**
     * Audio session management
     */
    suspend fun startAudioSession(): Result<Unit>
    suspend fun stopAudioSession(): Result<Unit>
    fun getAudioLevel(): Flow<Float>
}
