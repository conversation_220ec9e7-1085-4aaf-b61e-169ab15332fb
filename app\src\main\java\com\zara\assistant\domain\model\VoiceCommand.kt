package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Represents a voice command from the user
 */
@Parcelize
data class VoiceCommand(
    val id: String,
    val text: String,
    val timestamp: Date,
    val confidence: Float,
    val language: String,
    val isProcessed: Boolean = false,
    val processingTime: Long = 0L,
    val commandType: CommandType = CommandType.UNKNOWN
) : Parcelable

/**
 * Types of voice commands
 */
enum class CommandType {
    SYSTEM_CONTROL,     // Device settings, app control
    COMMUNICATION,      // Calls, messages
    INFORMATION,        // Weather, news, search
    ENTERTAINMENT,      // Music, videos
    CONVERSATION,       // General chat with AI
    UNKNOWN            // Unrecognized command
}
