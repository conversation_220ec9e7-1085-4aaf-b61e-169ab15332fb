package com.zara.assistant.presentation.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.zara.assistant.core.Constants
import com.zara.assistant.presentation.theme.VoiceListening
import com.zara.assistant.presentation.theme.VoiceProcessing
import com.zara.assistant.presentation.theme.VoiceSpeaking
import kotlin.random.Random

/**
 * Voice visualization component showing audio waves
 */
@Composable
fun VoiceVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float = 0f,
    modifier: Modifier = Modifier,
    barCount: Int = 20,
    barWidth: Dp = 4.dp,
    maxBarHeight: Dp = 60.dp,
    minBarHeight: Dp = 8.dp,
    barSpacing: Dp = 2.dp
) {
    val infiniteTransition = rememberInfiniteTransition(label = "voice_visualization")
    
    val color = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> VoiceListening
        Constants.VoiceState.PROCESSING -> VoiceProcessing
        Constants.VoiceState.SPEAKING -> VoiceSpeaking
        else -> Color.Gray.copy(alpha = 0.3f)
    }
    
    val isActive = voiceState in listOf(
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND,
        Constants.VoiceState.PROCESSING,
        Constants.VoiceState.SPEAKING
    )

    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(barSpacing, Alignment.CenterHorizontally),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(barCount) { index ->
            val animationDelay = index * 50
            
            val animatedHeight by infiniteTransition.animateFloat(
                initialValue = minBarHeight.value,
                targetValue = if (isActive) {
                    minBarHeight.value + (maxBarHeight.value - minBarHeight.value) * 
                    (0.3f + 0.7f * Random.nextFloat())
                } else {
                    minBarHeight.value
                },
                animationSpec = infiniteRepeatable(
                    animation = tween(
                        durationMillis = 800 + Random.nextInt(400),
                        delayMillis = animationDelay,
                        easing = LinearEasing
                    ),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "bar_height_$index"
            )
            
            VoiceBar(
                height = animatedHeight.dp,
                width = barWidth,
                color = color,
                isActive = isActive
            )
        }
    }
}

@Composable
private fun VoiceBar(
    height: Dp,
    width: Dp,
    color: Color,
    isActive: Boolean
) {
    Box(
        modifier = Modifier
            .width(width)
            .height(height)
    ) {
        Canvas(
            modifier = Modifier.matchParentSize()
        ) {
            val barHeight = size.height
            val barWidth = size.width
            
            drawLine(
                color = if (isActive) color else color.copy(alpha = 0.3f),
                start = androidx.compose.ui.geometry.Offset(barWidth / 2, 0f),
                end = androidx.compose.ui.geometry.Offset(barWidth / 2, barHeight),
                strokeWidth = barWidth,
                cap = StrokeCap.Round
            )
        }
    }
}

/**
 * Circular voice visualization (for compact display)
 */
@Composable
fun CircularVoiceVisualization(
    voiceState: Constants.VoiceState,
    audioLevel: Float = 0f,
    modifier: Modifier = Modifier,
    size: Dp = 100.dp,
    strokeWidth: Dp = 4.dp
) {
    val infiniteTransition = rememberInfiniteTransition(label = "circular_voice_visualization")
    
    val color = when (voiceState) {
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND -> VoiceListening
        Constants.VoiceState.PROCESSING -> VoiceProcessing
        Constants.VoiceState.SPEAKING -> VoiceSpeaking
        else -> Color.Gray.copy(alpha = 0.3f)
    }
    
    val isActive = voiceState in listOf(
        Constants.VoiceState.LISTENING_WAKE_WORD,
        Constants.VoiceState.LISTENING_COMMAND,
        Constants.VoiceState.PROCESSING,
        Constants.VoiceState.SPEAKING
    )
    
    val animatedRadius by infiniteTransition.animateFloat(
        initialValue = size.value * 0.3f,
        targetValue = if (isActive) size.value * 0.45f else size.value * 0.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "radius"
    )
    
    val animatedAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = if (isActive) 1f else 0.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(800, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )

    Canvas(
        modifier = modifier.size(size)
    ) {
        val center = androidx.compose.ui.geometry.Offset(this.size.width / 2, this.size.height / 2)
        
        // Draw multiple concentric circles for wave effect
        repeat(3) { index ->
            val radiusMultiplier = 1f + (index * 0.2f)
            drawCircle(
                color = color.copy(alpha = animatedAlpha / (index + 1)),
                radius = animatedRadius * radiusMultiplier,
                center = center,
                style = androidx.compose.ui.graphics.drawscope.Stroke(
                    width = strokeWidth.toPx() / (index + 1)
                )
            )
        }
        
        // Draw center dot
        drawCircle(
            color = color,
            radius = strokeWidth.toPx(),
            center = center
        )
    }
}
