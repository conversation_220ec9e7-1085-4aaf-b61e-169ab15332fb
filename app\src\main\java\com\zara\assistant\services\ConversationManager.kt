package com.zara.assistant.services

import android.util.Log
import com.zara.assistant.domain.model.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Universal Conversation Manager with Continuous Listening
 * Handles ALL conversation types with seamless clarification flow
 */
@Singleton
class ConversationManager @Inject constructor() {
    
    companion object {
        private const val TAG = "ConversationManager"
    }
    
    var currentContext: ConversationContext? = null
        private set
    private val _conversationState = MutableStateFlow<ConversationContext?>(null)
    val conversationState: StateFlow<ConversationContext?> = _conversationState.asStateFlow()
    
    /**
     * Start a new conversation
     */
    fun startConversation(): ConversationContext {
        Log.d(TAG, "🗣️ Starting new conversation")
        
        // Clear any existing conversation first
        endConversation()
        
        val context = ConversationContext(
            state = ConversationState.LISTENING
        )
        currentContext = context
        _conversationState.value = context
        
        Log.d(TAG, "✅ New conversation started with ID: ${context.conversationId}")
        return context
    }
    
    /**
     * Check if input is an exit command
     */
    private fun isExitCommand(input: String): Boolean {
        val lowerInput = input.lowercase().trim()
        return lowerInput in listOf(
            "bye", "goodbye", "bye bye", "see you later", "talk to you later",
            "stop", "exit", "quit", "end", "finish", "done",
            "bye zara", "goodbye zara", "stop zara", "that's all",
            "thank you", "thanks", "no more", "nothing else"
        )
    }

    /**
     * Check if input is a new command during conversation
     */
    private fun isNewCommand(input: String): Boolean {
        val lowerInput = input.lowercase().trim()
        return lowerInput.startsWith("open ") ||
               lowerInput.startsWith("turn on ") ||
               lowerInput.startsWith("turn off ") ||
               lowerInput.startsWith("play ") ||
               lowerInput.startsWith("send ") ||
               lowerInput.startsWith("tell me about ") ||
               lowerInput.startsWith("what is ") ||
               lowerInput.startsWith("explain ")
    }

    /**
     * Process a command during extended chat
     */
    private fun processCommandDuringChat(userInput: String, context: ConversationContext): CommandResult {
        Log.d(TAG, "🎯 Processing command during chat")

        val (type, parameters) = detectIntentAndExtractParameters(userInput)

        return if (parameters.isComplete()) {
            Log.d(TAG, "✅ Command complete, will execute and continue chat")
            CommandResult(
                isComplete = true,
                needsClarification = false,
                parameters = parameters.copy(intent = type),
                canExecute = true
            )
        } else {
            val question = generateClarificationQuestion(type, parameters)
            Log.d(TAG, "❓ Command incomplete, asking: $question")

            // Update context with new command but keep extended chat session
            val updatedContext = context.copy(
                type = type,
                parameters = parameters,
                state = ConversationState.WAITING_FOR_CLARIFICATION
            ).updateActivity()

            updateContext(updatedContext)

            CommandResult(
                isComplete = false,
                needsClarification = true,
                clarificationQuestion = question,
                parameters = parameters.copy(intent = type),
                canExecute = false
            )
        }
    }

    /**
     * Universal conversation processing - works for ALL command types
     */
    fun processUserInput(userInput: String): CommandResult {
        Log.d(TAG, "🎯 Processing user input: '$userInput'")

        // Check for exit commands first
        if (isExitCommand(userInput)) {
            Log.d(TAG, "👋 Exit command detected: '$userInput'")
            val response = when (userInput.lowercase().trim()) {
                "thank you", "thanks" -> "You're welcome! Talk to you later."
                "bye zara", "goodbye zara" -> "Goodbye! Have a great day."
                else -> "Goodbye! Talk to you later."
            }

            endConversation()
            return CommandResult(
                isComplete = true,
                needsClarification = false,
                parameters = CommandParameters(),
                canExecute = true
            )
        }

        val context = currentContext

        return when {
            context == null || context.state == ConversationState.IDLE -> {
                // Start new conversation
                Log.d(TAG, "🆕 Starting new conversation for: '$userInput'")
                val newContext = startConversation()
                processInitialCommand(userInput, newContext)
            }
            context.state == ConversationState.WAITING_FOR_CLARIFICATION -> {
                // Check if this is a new command during conversation
                if (context.sessionType == ConversationSessionType.EXTENDED_CHAT && isNewCommand(userInput)) {
                    Log.d(TAG, "🎯 New command during extended chat: '$userInput'")
                    processCommandDuringChat(userInput, context)
                } else {
                    // Universal clarification handling for ALL conversation types
                    Log.d(TAG, "🔄 Continuing conversation with clarification: '$userInput'")
                    Log.d(TAG, "📋 Conversation type: ${context.type}")
                    processClarificationResponse(userInput, context)
                }
            }
            else -> {
                Log.w(TAG, "⚠️ Unexpected state: ${context.state}, starting fresh")
                endConversation()
                val newContext = startConversation()
                processInitialCommand(userInput, newContext)
            }
        }
    }
    
    /**
     * Process initial command and extract parameters
     */
    private fun processInitialCommand(userInput: String, context: ConversationContext): CommandResult {
        Log.d(TAG, "🔍 Processing initial command")

        val (type, parameters) = detectIntentAndExtractParameters(userInput)

        // Determine session type based on command type
        val sessionType = when (type) {
            ConversationType.EXTENDED_CHAT -> ConversationSessionType.EXTENDED_CHAT
            ConversationType.EXIT_COMMAND -> ConversationSessionType.SINGLE_COMMAND
            else -> if (parameters.isComplete()) {
                ConversationSessionType.SINGLE_COMMAND
            } else {
                ConversationSessionType.CLARIFICATION_SEQUENCE
            }
        }

        val updatedContext = context.copy(
            type = type,
            sessionType = sessionType,
            parameters = parameters,
            state = ConversationState.PROCESSING
        ).updateActivity()

        updateContext(updatedContext)

        Log.d(TAG, "📋 Session type: $sessionType, Command type: $type")
        
        return if (parameters.isComplete()) {
            Log.d(TAG, "✅ Command is complete, ready to execute")
            CommandResult(
                isComplete = true,
                needsClarification = false,
                parameters = parameters,
                canExecute = true
            )
        } else {
            val question = generateClarificationQuestion(type, parameters)
            Log.d(TAG, "❓ Command incomplete, asking: $question")
            
            updateContext(updatedContext.copy(state = ConversationState.WAITING_FOR_CLARIFICATION))
            
            CommandResult(
                isComplete = false,
                needsClarification = true,
                clarificationQuestion = question,
                parameters = parameters,
                canExecute = false
            )
        }
    }
    
    /**
     * Universal clarification response processing for ALL conversation types
     */
    private fun processClarificationResponse(userInput: String, context: ConversationContext): CommandResult {
        Log.d(TAG, "🔄 Processing clarification response")
        
        val updatedContext = context.copy(
            state = ConversationState.PROCESSING
        ).updateActivity()
        
        updateContext(updatedContext)
        
        // Extract additional parameters based on conversation type
        val updatedParameters = extractAdditionalParameters(userInput, context.parameters, context.type)
        
        val finalContext = updatedContext.copy(parameters = updatedParameters)
        updateContext(finalContext)
        
        return if (updatedParameters.isComplete()) {
            Log.d(TAG, "✅ All parameters collected, ready to execute")
            CommandResult(
                isComplete = true,
                needsClarification = false,
                parameters = updatedParameters,
                canExecute = true
            )
        } else {
            val question = generateClarificationQuestion(context.type, updatedParameters)
            Log.d(TAG, "❓ Still need more info, asking: $question")
            
            updateContext(finalContext.copy(state = ConversationState.WAITING_FOR_CLARIFICATION))
            
            CommandResult(
                isComplete = false,
                needsClarification = true,
                clarificationQuestion = question,
                parameters = updatedParameters,
                canExecute = false
            )
        }
    }
    
    /**
     * Detect intent and extract initial parameters
     */
    private fun detectIntentAndExtractParameters(input: String): Pair<ConversationType, CommandParameters> {
        val lowerInput = input.lowercase().trim()
        
        return when {
            // Music control - prioritize music commands first
            lowerInput.contains("play") && (lowerInput.contains("song") || lowerInput.contains("music") ||
            lowerInput.matches(".*play\\s+[\\w\\s]+\\s+song.*".toRegex()) ||
            lowerInput.matches(".*play\\s+[\\w\\s]+\\s+by\\s+[\\w\\s]+.*".toRegex())) -> {
                val params = ParameterSchemas.MUSIC_CONTROL.deepCopy()
                params.addParameter("action", "play")
                extractSongInfo(input, params)
                Log.d(TAG, "🎵 Music control detected for: '$input'")
                ConversationType.MUSIC_CONTROL to params
            }

            // Pause/Stop music commands
            (lowerInput.contains("pause") || lowerInput.contains("stop")) &&
            (lowerInput.contains("music") || lowerInput.contains("song") || lowerInput.contains("playing")) -> {
                val params = ParameterSchemas.MUSIC_CONTROL.deepCopy()
                params.addParameter("action", if (lowerInput.contains("pause")) "pause" else "stop")
                Log.d(TAG, "🎵 Music control detected for: '$input'")
                ConversationType.MUSIC_CONTROL to params
            }

            // Next/Previous track commands
            (lowerInput.contains("next") || lowerInput.contains("previous") || lowerInput.contains("skip")) &&
            (lowerInput.contains("song") || lowerInput.contains("track") || lowerInput.contains("music")) -> {
                val params = ParameterSchemas.MUSIC_CONTROL.deepCopy()
                val action = when {
                    lowerInput.contains("next") || lowerInput.contains("skip") -> "next"
                    lowerInput.contains("previous") -> "previous"
                    else -> "next"
                }
                params.addParameter("action", action)
                Log.d(TAG, "🎵 Music control detected for: '$input'")
                ConversationType.MUSIC_CONTROL to params
            }

            // Information requests - expanded patterns for search, news, facts, etc.
            // Exclude music-related queries that start with "play"
            !lowerInput.startsWith("play") && (
            lowerInput.contains("tell me about") || lowerInput.contains("what is") ||
            lowerInput.contains("explain") || lowerInput.contains("information about") ||
            lowerInput.contains("search") || lowerInput.contains("find") ||
            lowerInput.contains("look up") || lowerInput.contains("news") ||
            lowerInput.contains("latest") || lowerInput.contains("current events") ||
            lowerInput.contains("who is") || lowerInput.contains("where is") ||
            lowerInput.contains("when is") || lowerInput.contains("how to") ||
            lowerInput.contains("definition of") || lowerInput.contains("meaning of")) -> {
                val topic = extractTopic(input)
                val params = ParameterSchemas.INFORMATION_REQUEST.deepCopy()
                Log.d(TAG, "📚 Information request detected. Topic: '$topic'")

                if (topic.isNotEmpty()) {
                    params.addParameter("topic", topic)
                    Log.d(TAG, "✅ Topic added to parameters: '$topic'")
                }

                ConversationType.INFORMATION_REQUEST to params
            }
            
            // Messaging
            lowerInput.contains("send") && (lowerInput.contains("message") || lowerInput.contains("text")) -> {
                val params = ParameterSchemas.MESSAGING.deepCopy()
                params.addParameter("action", "send")
                extractMessagingInfo(input, params)
                ConversationType.MESSAGING to params
            }
            
            // App control
            lowerInput.contains("open") && !lowerInput.contains("settings") -> {
                val params = ParameterSchemas.APP_CONTROL.deepCopy()
                params.addParameter("action", "open")
                extractAppName(input, params)
                ConversationType.APP_CONTROL to params
            }
            
            // System control - expanded patterns for time, date, device control
            (lowerInput.contains("turn on") || lowerInput.contains("turn off") ||
             lowerInput.contains("enable") || lowerInput.contains("disable") ||
             (lowerInput.contains("set") && (lowerInput.contains("volume") || lowerInput.contains("brightness"))) ||
             lowerInput.contains("what time") || lowerInput.contains("current time") ||
             lowerInput.contains("get time") || lowerInput.contains("time is it") ||
             lowerInput.contains("what date") || lowerInput.contains("current date") ||
             lowerInput.contains("get date") || lowerInput.contains("today's date") ||
             lowerInput.contains("wifi") || lowerInput.contains("bluetooth") ||
             lowerInput.contains("flashlight") || lowerInput.contains("torch")) -> {
                val params = ParameterSchemas.SYSTEM_CONTROL.deepCopy()
                extractSystemControlInfo(input, params)
                ConversationType.SYSTEM_CONTROL to params
            }
            
            // Extended chat/conversation
            (lowerInput.contains("talk with me") || lowerInput.contains("chat with me") || 
             lowerInput.contains("let's talk") || lowerInput.contains("let's chat") ||
             lowerInput.contains("have a conversation") || lowerInput.contains("conversation") ||
             lowerInput.contains("for") && (lowerInput.contains("minute") || lowerInput.contains("hour"))) -> {
                val params = ParameterSchemas.EXTENDED_CHAT.deepCopy()
                extractChatDuration(input, params)
                Log.d(TAG, "💬 Extended chat detected")
                ConversationType.EXTENDED_CHAT to params
            }
            
            else -> {
                ConversationType.GENERAL_CHAT to ParameterSchemas.GENERAL_CHAT.deepCopy()
            }
        }
    }
    
    /**
     * Extract topic from information request - enhanced for search queries
     */
    private fun extractTopic(input: String): String {
        val lowerInput = input.lowercase()

        return when {
            lowerInput.contains("tell me about ") -> {
                lowerInput.substringAfter("tell me about ", "").trim()
            }
            lowerInput.contains("what is ") -> {
                lowerInput.substringAfter("what is ", "").trim()
            }
            lowerInput.contains("explain ") -> {
                lowerInput.substringAfter("explain ", "").trim()
            }
            lowerInput.contains("information about ") -> {
                lowerInput.substringAfter("information about ", "").trim()
            }
            lowerInput.contains("search about ") -> {
                lowerInput.substringAfter("search about ", "").trim()
            }
            lowerInput.contains("search ") -> {
                lowerInput.substringAfter("search ", "").trim()
            }
            lowerInput.contains("find ") -> {
                lowerInput.substringAfter("find ", "").trim()
            }
            lowerInput.contains("look up ") -> {
                lowerInput.substringAfter("look up ", "").trim()
            }
            lowerInput.contains("who is ") -> {
                lowerInput.substringAfter("who is ", "").trim()
            }
            lowerInput.contains("where is ") -> {
                lowerInput.substringAfter("where is ", "").trim()
            }
            lowerInput.contains("when is ") -> {
                lowerInput.substringAfter("when is ", "").trim()
            }
            lowerInput.contains("how to ") -> {
                lowerInput.substringAfter("how to ", "").trim()
            }
            lowerInput.contains("definition of ") -> {
                lowerInput.substringAfter("definition of ", "").trim()
            }
            lowerInput.contains("meaning of ") -> {
                lowerInput.substringAfter("meaning of ", "").trim()
            }
            lowerInput.contains("latest ") -> {
                lowerInput.substringAfter("latest ", "").trim()
            }
            lowerInput.contains("news") -> {
                if (lowerInput.contains("latest news")) "latest news"
                else if (lowerInput.contains("current news")) "current news"
                else "news"
            }
            else -> input.trim() // Return the whole input if no specific pattern matches
        }
    }
    
    // Utility methods for conversation management
    fun isInConversation(): Boolean = currentContext != null && currentContext?.state != ConversationState.IDLE
    
    fun getCurrentState(): ConversationState = currentContext?.state ?: ConversationState.IDLE
    
    fun updateContext(context: ConversationContext) {
        currentContext = context
        _conversationState.value = context
    }
    
    fun addConversationTurn(userInput: String, zaraResponse: String) {
        currentContext?.let { context ->
            context.addTurn(userInput, zaraResponse)
            updateContext(context)
        }
    }
    
    fun endConversation() {
        currentContext?.let { context ->
            Log.d(TAG, "🏁 Ending conversation ${context.conversationId}")
            Log.d(TAG, "📊 Conversation had ${context.conversationHistory.size} turns")
        } ?: Log.d(TAG, "🏁 No active conversation to end")

        currentContext = null
        _conversationState.value = null
    }

    /**
     * Universal parameter extraction for ALL conversation types
     */
    private fun extractAdditionalParameters(input: String, currentParams: CommandParameters, type: ConversationType): CommandParameters {
        val updatedParams = currentParams.deepCopy()
        val missingParams = currentParams.getMissingRequiredParams()

        Log.d(TAG, "🔍 Extracting additional parameters from: '$input'")
        Log.d(TAG, "📋 Missing parameters: $missingParams")
        Log.d(TAG, "📋 Current extracted params: ${currentParams.extractedParams}")

        when (type) {
            ConversationType.INFORMATION_REQUEST -> {
                if ("topic" in missingParams) {
                    val topic = input.trim()
                    Log.d(TAG, "📚 Adding topic parameter: '$topic'")
                    updatedParams.addParameter("topic", topic)
                }
            }
            ConversationType.SYSTEM_CONTROL -> {
                if ("setting" in missingParams) {
                    val setting = extractSystemSetting(input)
                    if (setting.isNotEmpty()) {
                        Log.d(TAG, "⚙️ Adding setting parameter: '$setting'")
                        updatedParams.addParameter("setting", setting)
                    }
                }
            }
            ConversationType.APP_CONTROL -> {
                if ("app_name" in missingParams) {
                    val appName = input.trim()
                    Log.d(TAG, "📱 Adding app name parameter: '$appName'")
                    updatedParams.addParameter("app_name", appName)
                }
            }
            ConversationType.MESSAGING -> {
                if ("recipient" in missingParams) {
                    val recipient = input.trim()
                    Log.d(TAG, "👤 Adding recipient parameter: '$recipient'")
                    updatedParams.addParameter("recipient", recipient)
                } else if ("message_content" in missingParams) {
                    val message = input.trim()
                    Log.d(TAG, "💬 Adding message content parameter: '$message'")
                    updatedParams.addParameter("message_content", message)
                }
            }
            ConversationType.MUSIC_CONTROL -> {
                if ("song_name" in missingParams) {
                    val songName = input.trim()
                    Log.d(TAG, "🎵 Adding song name parameter: '$songName'")
                    updatedParams.addParameter("song_name", songName)
                }
            }
            ConversationType.EXTENDED_CHAT -> {
                // Extended chat doesn't need specific parameter extraction
                Log.d(TAG, "💬 Extended chat continuation")
            }
            else -> {
                Log.d(TAG, "⚠️ Unknown conversation type for parameter extraction: $type")
            }
        }

        Log.d(TAG, "✅ Updated parameters: ${updatedParams.extractedParams}")
        return updatedParams
    }

    /**
     * Generate clarification questions for ALL conversation types
     */
    private fun generateClarificationQuestion(type: ConversationType, params: CommandParameters): String {
        val missingParams = params.getMissingRequiredParams()
        if (missingParams.isEmpty()) return ""

        val firstMissing = missingParams.first()

        return when (type) {
            ConversationType.INFORMATION_REQUEST -> {
                when (firstMissing) {
                    "topic" -> "What would you like to know about?"
                    else -> "Could you be more specific about what you want to know?"
                }
            }
            ConversationType.SYSTEM_CONTROL -> {
                when (firstMissing) {
                    "action" -> "What would you like me to do?"
                    "setting" -> "Which setting would you like me to ${params.getParameter("action") ?: "change"}?"
                    else -> "Could you be more specific about the system setting?"
                }
            }
            ConversationType.APP_CONTROL -> {
                when (firstMissing) {
                    "action" -> "What would you like me to do with the app?"
                    "app_name" -> "Which app would you like me to ${params.getParameter("action") ?: "open"}?"
                    else -> "Could you specify which app you're referring to?"
                }
            }
            ConversationType.MESSAGING -> {
                when (firstMissing) {
                    "action" -> "What would you like me to do with messaging?"
                    "recipient" -> "Who would you like to send the message to?"
                    "message_content" -> "What message would you like to send?"
                    else -> "Could you provide more details about the message?"
                }
            }
            ConversationType.MUSIC_CONTROL -> {
                when (firstMissing) {
                    "action" -> "What would you like me to do with music?"
                    "song_name" -> "Which song would you like me to play?"
                    else -> "Could you specify which music you want?"
                }
            }
            ConversationType.EXTENDED_CHAT -> {
                "I'd love to chat with you! What's on your mind?"
            }
            else -> {
                "Could you provide more information about what you'd like me to do?"
            }
        }
    }

    // Helper extraction methods
    private fun extractSystemSetting(input: String): String {
        val lowerInput = input.lowercase()
        return when {
            lowerInput.contains("wifi") || lowerInput.contains("wi-fi") -> "wifi"
            lowerInput.contains("bluetooth") -> "bluetooth"
            lowerInput.contains("airplane") -> "airplane_mode"
            lowerInput.contains("mobile data") || lowerInput.contains("data") -> "mobile_data"
            lowerInput.contains("flashlight") || lowerInput.contains("torch") -> "flashlight"
            lowerInput.contains("volume") -> "volume"
            lowerInput.contains("brightness") -> "brightness"
            // Time and date settings
            lowerInput.contains("time") -> "time"
            lowerInput.contains("date") -> "date"
            else -> "" // Return empty string when no specific setting found
        }
    }

    /**
     * Extract value from system control commands (e.g., "set brightness to 80")
     */
    private fun extractSystemValue(input: String, setting: String): String? {
        val lowerInput = input.lowercase()

        return when (setting) {
            "volume" -> {
                // Extract volume level: "set volume to 80", "volume 50"
                val volumePattern = "(?:volume|to)\\s+(\\d+)".toRegex()
                volumePattern.find(lowerInput)?.groupValues?.get(1)
            }
            "brightness" -> {
                // Extract brightness level: "set brightness to 80", "brightness 50"
                val brightnessPattern = "(?:brightness|to)\\s+(\\d+)".toRegex()
                brightnessPattern.find(lowerInput)?.groupValues?.get(1)
            }
            else -> null
        }
    }

    private fun extractSongInfo(input: String, params: CommandParameters) {
        val lowerInput = input.lowercase().trim()

        // Try different patterns to extract song information
        val patterns = listOf(
            // "play [song name] by [artist]"
            "play\\s+(.+?)\\s+by\\s+(.+)".toRegex(RegexOption.IGNORE_CASE),
            // "play song [song name]"
            "play\\s+song\\s+(.+)".toRegex(RegexOption.IGNORE_CASE),
            // "play [song name] song"
            "play\\s+(.+?)\\s+song".toRegex(RegexOption.IGNORE_CASE),
            // "play [anything]"
            "play\\s+(.+)".toRegex(RegexOption.IGNORE_CASE)
        )

        for (pattern in patterns) {
            val match = pattern.find(input)
            if (match != null) {
                when (pattern.pattern) {
                    "play\\s+(.+?)\\s+by\\s+(.+)" -> {
                        // Song and artist
                        val songName = match.groupValues[1].trim()
                        val artistName = match.groupValues[2].trim()
                        if (songName.isNotEmpty()) params.addParameter("song_name", songName)
                        if (artistName.isNotEmpty()) params.addParameter("artist_name", artistName)
                        Log.d(TAG, "🎵 Extracted song: '$songName' by '$artistName'")
                    }
                    else -> {
                        // Just song name
                        val songName = match.groupValues[1].trim()
                        if (songName.isNotEmpty()) {
                            params.addParameter("song_name", songName)
                            Log.d(TAG, "🎵 Extracted song: '$songName'")
                        }
                    }
                }
                break
            }
        }
    }

    private fun extractMessagingInfo(input: String, params: CommandParameters) {
        // Extract recipient if present
        val recipientPattern = "send\\s+(?:message\\s+)?(?:to\\s+)?(.+)".toRegex(RegexOption.IGNORE_CASE)
        val match = recipientPattern.find(input)
        if (match != null) {
            val recipient = match.groupValues[1].trim()
            if (recipient.isNotEmpty()) {
                params.addParameter("recipient", recipient)
            }
        }
    }

    private fun extractAppName(input: String, params: CommandParameters) {
        // Extract app name if present
        val appPattern = "open\\s+(.+)".toRegex(RegexOption.IGNORE_CASE)
        val match = appPattern.find(input)
        if (match != null) {
            val appName = match.groupValues[1].trim()
            if (appName.isNotEmpty()) {
                params.addParameter("app_name", appName)
            }
        }
    }

    private fun extractSystemControlInfo(input: String, params: CommandParameters) {
        val lowerInput = input.lowercase()

        when {
            lowerInput.contains("turn on") -> params.addParameter("action", "turn on")
            lowerInput.contains("turn off") -> params.addParameter("action", "turn off")
            lowerInput.contains("enable") -> params.addParameter("action", "enable")
            lowerInput.contains("disable") -> params.addParameter("action", "disable")
            lowerInput.contains("set") -> params.addParameter("action", "set")
            // Time and date queries
            lowerInput.contains("time") -> params.addParameter("action", "get_time")
            lowerInput.contains("date") -> params.addParameter("action", "get_date")
        }

        val setting = extractSystemSetting(input)
        if (setting.isNotEmpty()) {
            params.addParameter("setting", setting)
            Log.d(TAG, "✅ System setting extracted: '$setting'")

            // Extract value if present (e.g., "set brightness to 80")
            val value = extractSystemValue(input, setting)
            if (value != null) {
                params.addParameter("value", value)
                Log.d(TAG, "✅ System value extracted: '$value'")
            }
        } else {
            Log.d(TAG, "⚠️ No system setting found in: '$input'")
        }
    }

    private fun extractChatDuration(input: String, params: CommandParameters) {
        val lowerInput = input.lowercase()

        // Extract duration if mentioned
        val durationPattern = "(\\d+)\\s*(minute|minutes|hour|hours)".toRegex()
        val match = durationPattern.find(lowerInput)
        if (match != null) {
            val duration = "${match.groupValues[1]} ${match.groupValues[2]}"
            params.addParameter("duration", duration)
            Log.d(TAG, "💬 Chat duration extracted: $duration")
        }
    }
}
