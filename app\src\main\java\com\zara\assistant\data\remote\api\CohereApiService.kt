package com.zara.assistant.data.remote.api

import com.zara.assistant.data.remote.dto.CohereGenerateRequest
import com.zara.assistant.data.remote.dto.CohereResponse
import com.zara.assistant.data.remote.dto.CohereClassifyResponse
import com.zara.assistant.data.remote.dto.CohereSummarizeResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Cohere API service interface
 */
interface CohereApiService {
    
    @POST("v1/generate")
    suspend fun generateText(
        @Body request: CohereGenerateRequest
    ): Response<CohereResponse>
    
    @POST("v1/classify")
    suspend fun classifyText(
        @Body request: CohereClassifyRequest
    ): Response<CohereClassifyResponse>
    
    @POST("v1/summarize")
    suspend fun summarizeText(
        @Body request: CohereSummarizeRequest
    ): Response<CohereSummarizeResponse>
}

/**
 * Cohere API request models
 */
data class CohereRequest(
    val model: String = "command",
    val prompt: String,
    val max_tokens: Int = 150,
    val temperature: Float = 0.7f,
    val k: Int = 0,
    val p: Float = 0.9f,
    val frequency_penalty: Float = 0.0f,
    val presence_penalty: Float = 0.0f,
    val end_sequences: List<String> = emptyList(),
    val stop_sequences: List<String> = emptyList(),
    val return_likelihoods: String = "NONE",
    val truncate: String = "END"
)

data class CohereClassifyRequest(
    val model: String = "embed-english-v2.0",
    val inputs: List<String>,
    val examples: List<CohereExample> = emptyList()
)

data class CohereExample(
    val text: String,
    val label: String
)

data class CohereSummarizeRequest(
    val text: String,
    val length: String = "medium",
    val format: String = "paragraph",
    val model: String = "summarize-medium",
    val extractiveness: String = "medium",
    val temperature: Float = 0.3f
)
