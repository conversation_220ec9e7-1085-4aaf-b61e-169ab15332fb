package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Represents the current state of the voice assistant
 */
@Parcelize
data class VoiceState(
    val currentState: State,
    val isWakeWordActive: <PERSON><PERSON>an,
    val isListening: <PERSON><PERSON>an,
    val isProcessing: <PERSON><PERSON>an,
    val isSpeaking: <PERSON>olean,
    val lastActivity: Date? = null,
    val currentCommand: String? = null,
    val errorMessage: String? = null,
    val confidence: Float = 0f
) : Parcelable {
    
    /**
     * Voice assistant states
     */
    enum class State {
        IDLE,                    // Ready to listen for wake word
        LISTENING_WAKE_WORD,     // Actively listening for "Hey <PERSON><PERSON>"
        LISTENING_COMMAND,       // Listening for user command
        PROCESSING_COMMAND,      // Processing the received command
        GENERATING_RESPONSE,     // AI is generating response
        SPEAKING_RESPONSE,       // T<PERSON> is speaking the response
        EXECUTING_ACTION,        // Performing system action
        ERROR,                   // Error state
        DISABLED                 // Voice assistant is disabled
    }
}

/**
 * Voice settings configuration
 */
@Parcelize
data class VoiceSettings(
    val isWakeWordEnabled: Boolean = true,
    val wakeWordSensitivity: Float = 0.5f,
    val speechRate: Float = 1.0f,
    val speechPitch: Float = 1.0f,
    val language: String = "en-US",
    val autoListenAfterResponse: Boolean = false,
    val voiceTimeout: Long = 10000L,
    val responseTimeout: Long = 15000L
) : Parcelable

/**
 * Audio configuration for voice processing
 */
@Parcelize
data class AudioConfig(
    val sampleRate: Int = 16000,
    val channelConfig: Int = android.media.AudioFormat.CHANNEL_IN_MONO,
    val audioFormat: Int = android.media.AudioFormat.ENCODING_PCM_16BIT,
    val bufferSize: Int = 1024,
    val noiseSuppressionEnabled: Boolean = true,
    val echoCancellationEnabled: Boolean = true,
    val automaticGainControlEnabled: Boolean = true
) : Parcelable
