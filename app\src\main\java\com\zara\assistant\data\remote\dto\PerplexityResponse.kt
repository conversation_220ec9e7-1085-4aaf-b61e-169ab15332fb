package com.zara.assistant.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * Perplexity API response models
 */
data class PerplexityResponse(
    val id: String,
    val model: String,
    val created: Long,
    val usage: PerplexityUsage,
    @SerializedName("object")
    val objectType: String,
    val choices: List<PerplexityChoice>
)

data class PerplexityChoice(
    val index: Int,
    val finish_reason: String,
    val message: PerplexityResponseMessage,
    val delta: PerplexityDelta?
)

data class PerplexityResponseMessage(
    val role: String,
    val content: String
)

data class PerplexityDelta(
    val role: String?,
    val content: String?
)

data class PerplexityUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * Error response model
 */
data class PerplexityErrorResponse(
    val error: PerplexityError
)

data class PerplexityError(
    val message: String,
    val type: String,
    val code: String?
)
