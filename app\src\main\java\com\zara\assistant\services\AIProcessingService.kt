package com.zara.assistant.services

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.AIResponse
import com.zara.assistant.domain.repository.AIRepository
import com.zara.assistant.domain.repository.ConversationRepository
import com.zara.assistant.core.Constants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Service for handling AI processing and response generation
 */
@AndroidEntryPoint
class AIProcessingService : Service() {

    companion object {
        private const val TAG = "AIProcessingService"
        
        const val ACTION_PROCESS_COMMAND = "PROCESS_COMMAND"
        const val EXTRA_VOICE_COMMAND = "voice_command"
        
        fun processCommand(context: Context, command: VoiceCommand) {
            val intent = Intent(context, AIProcessingService::class.java).apply {
                action = ACTION_PROCESS_COMMAND
                putExtra(EXTRA_VOICE_COMMAND, command)
            }
            context.startService(intent)
        }
    }

    private val binder = AIProcessingBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    private val _processingState = MutableStateFlow(false)
    val processingState: StateFlow<Boolean> = _processingState.asStateFlow()
    
    private val _lastResponse = MutableStateFlow<AIResponse?>(null)
    val lastResponse: StateFlow<AIResponse?> = _lastResponse.asStateFlow()

    @Inject
    lateinit var aiRepository: AIRepository
    
    @Inject
    lateinit var conversationRepository: ConversationRepository

    inner class AIProcessingBinder : Binder() {
        fun getService(): AIProcessingService = this@AIProcessingService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_PROCESS_COMMAND -> {
                val command = intent.getParcelableExtra<VoiceCommand>(EXTRA_VOICE_COMMAND)
                if (command != null) {
                    processVoiceCommand(command)
                }
            }
        }
        return START_NOT_STICKY
    }

    private fun processVoiceCommand(command: VoiceCommand) {
        serviceScope.launch {
            try {
                _processingState.value = true
                Log.d(TAG, "Processing command: ${command.text}")
                
                // Get conversation history for context
                val conversation = conversationRepository.getActiveConversation()
                val history = if (conversation != null) {
                    conversationRepository.getConversationHistory(conversation.id)
                } else {
                    emptyList()
                }
                
                // Process the command
                val result = aiRepository.processCommand(command)
                
                result.onSuccess { response ->
                    Log.d(TAG, "AI response generated: ${response.text}")
                    _lastResponse.value = response
                    
                    // Broadcast the response
                    val responseIntent = Intent("com.zara.assistant.AI_RESPONSE").apply {
                        putExtra("response", response)
                    }
                    sendBroadcast(responseIntent)
                    
                }.onFailure { error ->
                    Log.e(TAG, "Failed to process command", error)
                    
                    // Generate fallback response
                    val fallbackResponse = aiRepository.getFallbackResponse(command)
                    _lastResponse.value = fallbackResponse
                    
                    // Broadcast the fallback response
                    val responseIntent = Intent("com.zara.assistant.AI_RESPONSE").apply {
                        putExtra("response", fallbackResponse)
                        putExtra("error", error.message)
                    }
                    sendBroadcast(responseIntent)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in AI processing", e)
                
                // Handle unexpected errors
                val errorResponse = aiRepository.handleAPIError(e, command)
                _lastResponse.value = errorResponse
                
                val errorIntent = Intent("com.zara.assistant.AI_RESPONSE").apply {
                    putExtra("response", errorResponse)
                    putExtra("error", e.message)
                }
                sendBroadcast(errorIntent)
                
            } finally {
                _processingState.value = false
                stopSelf()
            }
        }
    }

    suspend fun generateResponse(
        command: VoiceCommand,
        personality: Constants.AIPersonality = Constants.AIPersonality.FRIENDLY,
        responseStyle: Constants.AIResponseStyle = Constants.AIResponseStyle.CONVERSATIONAL
    ): Result<AIResponse> {
        return try {
            _processingState.value = true
            
            val conversation = conversationRepository.getActiveConversation()
            val history = if (conversation != null) {
                conversationRepository.getConversationHistory(conversation.id)
            } else {
                emptyList()
            }
            
            aiRepository.generateResponse(command, history, personality, responseStyle)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating response", e)
            Result.failure(e)
        } finally {
            _processingState.value = false
        }
    }

    suspend fun classifyCommand(text: String): Result<com.zara.assistant.domain.model.CommandType> {
        return aiRepository.classifyCommand(text)
    }

    suspend fun checkAIHealth(): Pair<Boolean, Boolean> {
        val cohereHealth = aiRepository.checkCohereHealth().getOrElse { false }
        val perplexityHealth = aiRepository.checkPerplexityHealth().getOrElse { false }
        return Pair(cohereHealth, perplexityHealth)
    }

    fun isProcessing(): Boolean = _processingState.value

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "AIProcessingService destroyed")
    }
}
