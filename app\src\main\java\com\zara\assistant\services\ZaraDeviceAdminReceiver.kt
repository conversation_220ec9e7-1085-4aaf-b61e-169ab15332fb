package com.zara.assistant.services

import android.app.admin.DeviceAdminReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * Device Admin Receiver for enhanced system control capabilities
 */
class ZaraDeviceAdminReceiver : DeviceAdminReceiver() {

    companion object {
        private const val TAG = "ZaraDeviceAdmin"
    }

    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        Log.d(TAG, "✅ Zara Device Admin enabled - Enhanced system control available")
    }

    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
        Log.d(TAG, "❌ Zara Device Admin disabled - Falling back to standard control")
    }

    override fun onDisableRequested(context: Context, intent: Intent): CharSequence {
        Log.d(TAG, "⚠️ Device Admin disable requested")
        return "Disabling Zara Device Admin will reduce voice control capabilities for WiFi and system settings."
    }
}
