package com.zara.assistant.services

import android.content.Context
import android.util.Log
import com.zara.assistant.data.local.dao.UserLearningDao
import com.zara.assistant.domain.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Personal memory and recall service for intelligent conversation
 */
@Singleton
class PersonalMemoryService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userLearningDao: UserLearningDao
) {
    companion object {
        private const val TAG = "PersonalMemoryService"
    }

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    var userProfile: UserProfile? = null
        private set

    /**
     * Initialize memory service and load user profile
     */
    suspend fun initialize() {
        try {
            userProfile = userLearningDao.getUserProfile()
            if (userProfile == null) {
                // Create default user profile
                userProfile = UserProfile()
                userLearningDao.insertUserProfile(userProfile!!)
            }
            Log.d(TAG, "🧠 Memory service initialized for user: ${userProfile?.name ?: "Unknown"}")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error initializing memory service", e)
        }
    }

    /**
     * Extract and store personal information from conversation
     */
    suspend fun extractAndStorePersonalInfo(userInput: String, context: InteractionContext) {
        serviceScope.launch {
            try {
                val personalInfo = extractPersonalInformation(userInput)
                
                personalInfo.forEach { (type, value) ->
                    when (type) {
                        "name" -> updateUserName(value)
                        "age" -> updateUserAge(value)
                        "occupation" -> updateUserOccupation(value)
                        "location" -> updateUserLocation(value)
                        "favorite_food" -> storeFavorite("food", value)
                        "favorite_music" -> storeFavorite("music", value)
                        "favorite_app" -> storeFavorite("app", value)
                        "hobby" -> storeFavorite("hobby", value)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ Error extracting personal info", e)
            }
        }
    }

    /**
     * Generate personalized greeting based on stored information
     */
    suspend fun generatePersonalizedGreeting(): String {
        return try {
            val profile = userProfile ?: userLearningDao.getUserProfile()
            val calendar = Calendar.getInstance()
            val hour = calendar.get(Calendar.HOUR_OF_DAY)
            
            val timeGreeting = when (hour) {
                in 5..11 -> "Good morning"
                in 12..17 -> "Good afternoon"
                in 18..21 -> "Good evening"
                else -> "Hello"
            }
            
            val personalizedPart = when {
                profile?.name != null -> "$timeGreeting ${profile.name}!"
                else -> "$timeGreeting!"
            }
            
            // Add contextual information based on patterns
            val contextualInfo = getContextualInfo()
            
            if (contextualInfo.isNotEmpty()) {
                "$personalizedPart $contextualInfo"
            } else {
                personalizedPart
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating personalized greeting", e)
            "Hello! How can I help you today?"
        }
    }

    /**
     * Recall relevant information for conversation context
     */
    suspend fun recallRelevantInfo(topic: String): List<MemoryItem> {
        return try {
            val relevantMemories = mutableListOf<MemoryItem>()
            
            // Search favorites related to topic
            val favorites = userLearningDao.getFavoritesByCategory(topic)
            favorites.forEach { favorite: UserFavorite ->
                relevantMemories.add(
                    MemoryItem(
                        type = "favorite",
                        content = "You mentioned you like ${favorite.item}",
                        confidence = favorite.confidence,
                        source = favorite.source
                    )
                )
            }
            
            // Search conversation history for related topics
            val conversations = userLearningDao.getConversationsByTopic(topic)
            conversations.take(3).forEach { conversation: ConversationHistory ->
                relevantMemories.add(
                    MemoryItem(
                        type = "conversation",
                        content = "We talked about this before: ${conversation.userInput}",
                        confidence = 0.8f,
                        source = "conversation_history"
                    )
                )
            }
            
            relevantMemories.sortedByDescending { it.confidence }
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error recalling relevant info", e)
            emptyList()
        }
    }

    /**
     * Generate proactive suggestions based on memory
     */
    suspend fun generateProactiveSuggestions(): List<ProactiveSuggestion> {
        return try {
            val suggestions = mutableListOf<ProactiveSuggestion>()
            val calendar = Calendar.getInstance()
            val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
            
            // Time-based suggestions
            val timeBasedSuggestion = generateTimeBasedSuggestion(currentHour)
            if (timeBasedSuggestion != null) {
                suggestions.add(timeBasedSuggestion)
            }
            
            // Favorite-based suggestions
            val favoriteSuggestions = generateFavoriteSuggestions()
            suggestions.addAll(favoriteSuggestions)
            
            // Pattern-based suggestions
            val patternSuggestions = generatePatternSuggestions()
            suggestions.addAll(patternSuggestions)
            
            suggestions.take(3) // Limit to 3 suggestions
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating proactive suggestions", e)
            emptyList()
        }
    }

    /**
     * Store conversation for future recall
     */
    suspend fun storeConversation(
        sessionId: String,
        userInput: String,
        zaraResponse: String,
        conversationType: String,
        success: Boolean
    ) {
        try {
            val topics = extractTopics(userInput + " " + zaraResponse)
            
            val conversation = ConversationHistory(
                sessionId = sessionId,
                userInput = userInput,
                zaraResponse = zaraResponse,
                timestamp = System.currentTimeMillis(),
                conversationType = conversationType,
                success = success,
                topics = topics.joinToString(",")
            )
            
            userLearningDao.insertConversationHistory(conversation)
            
            // Extract personal information from this conversation
            extractAndStorePersonalInfo(userInput, getCurrentContext())
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error storing conversation", e)
        }
    }

    private fun extractPersonalInformation(input: String): Map<String, String> {
        val personalInfo = mutableMapOf<String, String>()
        val lowerInput = input.lowercase()
        
        // Name extraction
        val namePatterns = listOf(
            "my name is (\\w+)".toRegex(),
            "i'm (\\w+)".toRegex(),
            "call me (\\w+)".toRegex()
        )
        namePatterns.forEach { pattern ->
            pattern.find(lowerInput)?.let { match ->
                personalInfo["name"] = match.groupValues[1].capitalize()
            }
        }
        
        // Age extraction
        "i am (\\d+) years old".toRegex().find(lowerInput)?.let { match ->
            personalInfo["age"] = match.groupValues[1]
        }
        
        // Occupation extraction
        "i work as a (\\w+)".toRegex().find(lowerInput)?.let { match ->
            personalInfo["occupation"] = match.groupValues[1]
        }
        
        // Favorite food
        "i love (\\w+)".toRegex().find(lowerInput)?.let { match ->
            val item = match.groupValues[1]
            if (isFoodItem(item)) {
                personalInfo["favorite_food"] = item
            }
        }
        
        // Location
        "i live in (\\w+)".toRegex().find(lowerInput)?.let { match ->
            personalInfo["location"] = match.groupValues[1]
        }
        
        return personalInfo
    }

    private suspend fun updateUserName(name: String) {
        try {
            val currentProfile = userProfile ?: return
            val updatedProfile = currentProfile.copy(
                name = name,
                lastUpdated = System.currentTimeMillis()
            )
            userLearningDao.updateUserProfile(updatedProfile)
            userProfile = updatedProfile
            Log.d(TAG, "👤 Updated user name: $name")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating user name", e)
        }
    }

    private suspend fun updateUserAge(age: String) {
        try {
            val currentProfile = userProfile ?: return
            val updatedProfile = currentProfile.copy(
                age = age.toIntOrNull(),
                lastUpdated = System.currentTimeMillis()
            )
            userLearningDao.updateUserProfile(updatedProfile)
            userProfile = updatedProfile
            Log.d(TAG, "🎂 Updated user age: $age")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating user age", e)
        }
    }

    private suspend fun updateUserOccupation(occupation: String) {
        try {
            val currentProfile = userProfile ?: return
            val updatedProfile = currentProfile.copy(
                occupation = occupation,
                lastUpdated = System.currentTimeMillis()
            )
            userLearningDao.updateUserProfile(updatedProfile)
            userProfile = updatedProfile
            Log.d(TAG, "💼 Updated user occupation: $occupation")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating user occupation", e)
        }
    }

    private suspend fun updateUserLocation(location: String) {
        try {
            val currentProfile = userProfile ?: return
            val updatedProfile = currentProfile.copy(
                location = location,
                lastUpdated = System.currentTimeMillis()
            )
            userLearningDao.updateUserProfile(updatedProfile)
            userProfile = updatedProfile
            Log.d(TAG, "📍 Updated user location: $location")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error updating user location", e)
        }
    }

    private suspend fun storeFavorite(category: String, item: String) {
        try {
            val favorite = UserFavorite(
                category = category,
                item = item,
                confidence = 0.8f,
                source = "conversation"
            )
            userLearningDao.insertUserFavorite(favorite)
            Log.d(TAG, "❤️ Stored favorite $category: $item")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error storing favorite", e)
        }
    }

    private suspend fun getContextualInfo(): String {
        return try {
            val patterns = userLearningDao.getActiveBehavioralPatterns()
            val calendar = Calendar.getInstance()
            val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
            
            // Find relevant patterns for current time
            val relevantPattern = patterns.find { pattern: BehavioralPattern ->
                pattern.timePattern?.contains(currentHour.toString()) == true
            }

            relevantPattern?.let { pattern: BehavioralPattern ->
                when {
                    pattern.description.contains("coffee") -> "Ready for your usual coffee?"
                    pattern.description.contains("music") -> "Time for some music?"
                    pattern.description.contains("news") -> "Want to catch up on the news?"
                    else -> ""
                }
            } ?: ""
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error getting contextual info", e)
            ""
        }
    }

    private suspend fun generateTimeBasedSuggestion(hour: Int): ProactiveSuggestion? {
        return try {
            when (hour) {
                in 7..9 -> {
                    val favorites = userLearningDao.getFavoritesByCategory("morning_routine")
                    if (favorites.isNotEmpty()) {
                        ProactiveSuggestion(
                            title = "Morning routine",
                            description = "Ready to start your day with ${favorites.first().item}?",
                            command = "start morning routine",
                            confidence = 0.8f,
                            basedOnPattern = "Morning time pattern"
                        )
                    } else null
                }
                in 18..20 -> {
                    ProactiveSuggestion(
                        title = "Evening wind down",
                        description = "Time to relax? I can play some music or check tomorrow's weather.",
                        command = "evening routine",
                        confidence = 0.7f,
                        basedOnPattern = "Evening time pattern"
                    )
                }
                else -> null
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating time-based suggestion", e)
            null
        }
    }

    private suspend fun generateFavoriteSuggestions(): List<ProactiveSuggestion> {
        return try {
            val topFavorites = userLearningDao.getTopFavorites(3)
            topFavorites.map { favorite: UserFavorite ->
                ProactiveSuggestion(
                    title = "Your favorite ${favorite.category}",
                    description = "Would you like me to help with ${favorite.item}?",
                    command = "open ${favorite.item}",
                    confidence = favorite.confidence,
                    basedOnPattern = "User favorites"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating favorite suggestions", e)
            emptyList()
        }
    }

    private suspend fun generatePatternSuggestions(): List<ProactiveSuggestion> {
        return try {
            val patterns = userLearningDao.getActiveBehavioralPatterns()
            patterns.take(2).map { pattern: BehavioralPattern ->
                ProactiveSuggestion(
                    title = "Based on your habits",
                    description = pattern.description,
                    command = extractCommandFromPattern(pattern),
                    confidence = pattern.confidence,
                    basedOnPattern = pattern.description
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error generating pattern suggestions", e)
            emptyList()
        }
    }

    private fun extractTopics(text: String): List<String> {
        val commonTopics = listOf(
            "weather", "music", "news", "food", "travel", "work", "family",
            "sports", "technology", "health", "entertainment", "shopping"
        )
        return commonTopics.filter { text.lowercase().contains(it) }
    }

    private fun getCurrentContext(): InteractionContext {
        val calendar = Calendar.getInstance()
        return InteractionContext(
            timeOfDay = calendar.get(Calendar.HOUR_OF_DAY),
            dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        )
    }

    private fun isFoodItem(item: String): Boolean {
        val foodItems = listOf("pizza", "burger", "pasta", "sushi", "coffee", "tea", "chocolate")
        return foodItems.contains(item.lowercase())
    }

    private fun extractCommandFromPattern(pattern: BehavioralPattern): String {
        // Extract actionable command from pattern description
        return when {
            pattern.description.contains("music") -> "play music"
            pattern.description.contains("weather") -> "check weather"
            pattern.description.contains("news") -> "get news"
            else -> "help"
        }
    }

    fun cleanup() {
        serviceScope.cancel()
    }
}

/**
 * Memory item data class
 */
data class MemoryItem(
    val type: String,
    val content: String,
    val confidence: Float,
    val source: String
)
