package com.zara.assistant.di

import android.app.NotificationManager
import android.content.Context
import android.content.SharedPreferences
import android.speech.tts.TextToSpeech
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.zara.assistant.data.local.preferences.PreferencesManager
import com.zara.assistant.data.repository.AIRepositoryImpl
import com.zara.assistant.data.repository.ConversationRepositoryImpl
import com.zara.assistant.data.repository.SettingsRepositoryImpl
import com.zara.assistant.data.repository.VoiceRepositoryImpl
import com.zara.assistant.domain.repository.AIRepository
import com.zara.assistant.domain.repository.ConversationRepository
import com.zara.assistant.domain.repository.SettingsRepository
import com.zara.assistant.domain.repository.VoiceRepository
import com.zara.assistant.utils.ApiKeyManager
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

// DataStore extension
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "zara_preferences")

/**
 * Main dependency injection module for the application
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AppModule {

    @Binds
    abstract fun bindAIRepository(aiRepositoryImpl: AIRepositoryImpl): AIRepository

    @Binds
    abstract fun bindConversationRepository(conversationRepositoryImpl: ConversationRepositoryImpl): ConversationRepository

    @Binds
    abstract fun bindSettingsRepository(settingsRepositoryImpl: SettingsRepositoryImpl): SettingsRepository

    @Binds
    abstract fun bindVoiceRepository(voiceRepositoryImpl: VoiceRepositoryImpl): VoiceRepository

    companion object {

        @Provides
        @Singleton
        fun provideDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
            return context.dataStore
        }

        @Provides
        @Singleton
        fun provideSharedPreferences(@ApplicationContext context: Context): SharedPreferences {
            return context.getSharedPreferences("zara_prefs", Context.MODE_PRIVATE)
        }

        @Provides
        @Singleton
        fun providePreferencesManager(dataStore: DataStore<Preferences>): PreferencesManager {
            return PreferencesManager(dataStore)
        }

        @Provides
        @Singleton
        fun provideTextToSpeech(@ApplicationContext context: Context): TextToSpeech {
            return TextToSpeech(context) { status ->
                if (status == TextToSpeech.SUCCESS) {
                    // TTS initialization successful
                } else {
                    // TTS initialization failed
                }
            }
        }

        @Provides
        @Singleton
        fun provideGson(): Gson {
            return GsonBuilder()
                .setLenient()
                .create()
        }

        @Provides
        @Singleton
        fun provideNotificationManager(@ApplicationContext context: Context): NotificationManager {
            return context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }

        @Provides
        @Singleton
        fun provideApiKeyManager(@ApplicationContext context: Context): ApiKeyManager {
            return ApiKeyManager(context).apply {
                initialize()
            }
        }
    }
}
