package com.zara.assistant.di

import android.content.Context
import com.zara.assistant.data.local.dao.UserLearningDao
import com.zara.assistant.services.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Singleton

/**
 * Dependency injection module for service-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {

    @Provides
    @Singleton
    fun providePersonalMemoryService(
        @ApplicationContext context: Context,
        userLearningDao: UserLearningDao
    ): PersonalMemoryService {
        return PersonalMemoryService(context, userLearningDao)
    }

    @Provides
    @Singleton
    fun provideMLPersonalizationService(
        @ApplicationContext context: Context,
        userLearningDao: UserLearningDao
    ): MLPersonalizationService {
        return MLPersonalizationService(context, userLearningDao)
    }

    @Provides
    @Singleton
    fun provideWebSearchService(
        @ApplicationContext context: Context,
        userLearningDao: UserLearningDao,
        okHttpClient: OkHttpClient
    ): WebSearchService {
        return WebSearchService(context, userLearningDao, okHttpClient)
    }

    // UserLearningService is provided automatically by Hilt as it's an @AndroidEntryPoint Service
    // No need to provide it manually

    @Provides
    @Singleton
    fun provideAIOrchestrationService(
        @ApplicationContext context: Context,
        personalMemoryService: PersonalMemoryService,
        mlPersonalizationService: MLPersonalizationService,
        webSearchService: WebSearchService
    ): AIOrchestrationService {
        return AIOrchestrationService(
            context,
            personalMemoryService,
            mlPersonalizationService,
            webSearchService
        )
    }
}
