package com.zara.assistant.domain.model

import java.util.*

/**
 * Conversation state management
 */
enum class ConversationState {
    IDLE,                    // No active conversation
    LISTENING,              // Actively listening for user input
    PROCESSING,             // Processing user command
    WAITING_FOR_CLARIFICATION, // Asked a question, waiting for response
    EXECUTING               // Executing a complete command
}

/**
 * Types of conversations/intents
 */
enum class ConversationType {
    INFORMATION_REQUEST,    // "tell me about..."
    MUSIC_CONTROL,         // "play song..."
    MESSAGING,             // "send message..."
    APP_CONTROL,           // "open app..."
    SYSTEM_CONTROL,        // "turn on wifi..."
    EXTENDED_CHAT,         // "talk with me", "let's chat", etc.
    GENERAL_CHAT,          // General conversation
    EXIT_COMMAND,          // "bye", "stop", etc.
    UNKNOWN                // Unrecognized intent
}

/**
 * Conversation session types
 */
enum class ConversationSessionType {
    SINGLE_COMMAND,        // Execute one command and end
    EXTENDED_CHAT,         // Keep conversation active until exit
    CLARIFICATION_SEQUENCE // Keep active until command complete
}

/**
 * Command parameters for different conversation types
 */
data class CommandParameters(
    val intent: ConversationType = ConversationType.UNKNOWN,
    val extractedParams: MutableMap<String, String> = mutableMapOf(),
    val requiredParams: List<String> = emptyList(),
    val optionalParams: List<String> = emptyList()
) {
    fun getMissingRequiredParams(): List<String> {
        return requiredParams.filter { !extractedParams.containsKey(it) }
    }
    
    fun isComplete(): Boolean {
        return getMissingRequiredParams().isEmpty()
    }
    
    fun addParameter(key: String, value: String) {
        extractedParams[key] = value
    }
    
    fun getParameter(key: String): String? {
        return extractedParams[key]
    }
    
    /**
     * Create a proper deep copy with new MutableMap instance
     */
    fun deepCopy(): CommandParameters {
        return CommandParameters(
            intent = intent,
            extractedParams = extractedParams.toMutableMap(), // Create new MutableMap
            requiredParams = requiredParams.toList(), // Create new List
            optionalParams = optionalParams.toList() // Create new List
        )
    }
}

/**
 * Conversation turn for history tracking
 */
data class ConversationTurn(
    val userInput: String,
    val zaraResponse: String,
    val timestamp: Date = Date()
)

/**
 * Conversation context
 */
data class ConversationContext(
    val conversationId: String = UUID.randomUUID().toString(),
    val type: ConversationType = ConversationType.UNKNOWN,
    val sessionType: ConversationSessionType = ConversationSessionType.SINGLE_COMMAND,
    val state: ConversationState = ConversationState.IDLE,
    val parameters: CommandParameters = CommandParameters(),
    val conversationHistory: MutableList<ConversationTurn> = mutableListOf(),
    val startTime: Date = Date(),
    val lastActivity: Date = Date()
) {
    fun addTurn(userInput: String, zaraResponse: String) {
        conversationHistory.add(ConversationTurn(userInput, zaraResponse))
    }

    fun updateActivity(): ConversationContext {
        return copy(lastActivity = Date())
    }

    fun shouldContinueAfterExecution(): Boolean {
        return sessionType == ConversationSessionType.EXTENDED_CHAT
    }
}

/**
 * Command execution result
 */
data class CommandResult(
    val isComplete: Boolean,
    val needsClarification: Boolean,
    val clarificationQuestion: String? = null,
    val parameters: CommandParameters,
    val canExecute: Boolean = false
)

/**
 * Parameter schemas for different conversation types
 */
object ParameterSchemas {
    val INFORMATION_REQUEST = CommandParameters(
        intent = ConversationType.INFORMATION_REQUEST,
        requiredParams = listOf("topic"),
        optionalParams = listOf("specific_aspect", "detail_level")
    )
    
    val MUSIC_CONTROL = CommandParameters(
        intent = ConversationType.MUSIC_CONTROL,
        requiredParams = listOf("action"),
        optionalParams = listOf("song_name", "artist", "album", "genre")
    )
    
    val MESSAGING = CommandParameters(
        intent = ConversationType.MESSAGING,
        requiredParams = listOf("action", "recipient"),
        optionalParams = listOf("message_content", "platform")
    )
    
    val APP_CONTROL = CommandParameters(
        intent = ConversationType.APP_CONTROL,
        requiredParams = listOf("action", "app_name"),
        optionalParams = listOf("specific_function")
    )
    
    val SYSTEM_CONTROL = CommandParameters(
        intent = ConversationType.SYSTEM_CONTROL,
        requiredParams = listOf("action", "setting"),
        optionalParams = listOf("value")
    )
    
    val EXTENDED_CHAT = CommandParameters(
        intent = ConversationType.EXTENDED_CHAT,
        requiredParams = emptyList(),
        optionalParams = listOf("duration", "topic")
    )
    
    val GENERAL_CHAT = CommandParameters(
        intent = ConversationType.GENERAL_CHAT,
        requiredParams = emptyList(),
        optionalParams = listOf("context", "mood")
    )
}
