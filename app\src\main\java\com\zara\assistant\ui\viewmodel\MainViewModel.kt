package com.zara.assistant.ui.viewmodel

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.VoiceState
import com.zara.assistant.domain.repository.VoiceRepository
import com.zara.assistant.domain.repository.SettingsRepository
import com.zara.assistant.services.AdvancedVoiceProcessingService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the main screen
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val voiceRepository: VoiceRepository,
    private val settingsRepository: SettingsRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    private val sharedPreferences: SharedPreferences by lazy {
        context.getSharedPreferences("zara_prefs", Context.MODE_PRIVATE)
    }

    companion object {
        private const val KEY_FIRST_LAUNCH = "is_first_launch"
        private const val KEY_ONBOARDING_COMPLETED = "onboarding_completed"
    }

    init {
        observeVoiceState()
        observeSettings()
        checkFirstLaunch()
    }

    private fun observeVoiceState() {
        viewModelScope.launch {
            combine(
                voiceRepository.getVoiceState(),
                voiceRepository.isWakeWordDetectionActive()
            ) { voiceState, isWakeWordActive ->
                _uiState.value = _uiState.value.copy(
                    voiceState = voiceState,
                    isWakeWordActive = isWakeWordActive,
                    voiceStateText = getVoiceStateText(voiceState)
                )
            }
        }
    }

    private fun observeSettings() {
        viewModelScope.launch {
            settingsRepository.observeVoiceSettings().collect { settings ->
                _uiState.value = _uiState.value.copy(
                    isWakeWordEnabled = settings.isWakeWordEnabled,
                    wakeWordSensitivity = settings.wakeWordSensitivity
                )
            }
        }
    }

    /**
     * Check if this is the first launch and update UI state accordingly
     */
    private fun checkFirstLaunch() {
        val isFirstLaunch = sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, true)
        val onboardingCompleted = sharedPreferences.getBoolean(KEY_ONBOARDING_COMPLETED, false)

        _uiState.value = _uiState.value.copy(
            isFirstLaunch = isFirstLaunch && !onboardingCompleted
        )
    }

    /**
     * Mark onboarding as completed
     */
    fun completeOnboarding() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_LAUNCH, false)
            .putBoolean(KEY_ONBOARDING_COMPLETED, true)
            .apply()

        _uiState.value = _uiState.value.copy(
            isFirstLaunch = false
        )
    }

    fun onVoiceButtonClicked() {
        viewModelScope.launch {
            when (_uiState.value.voiceState.currentState) {
                VoiceState.State.IDLE -> {
                    // Start manual listening
                    startListening()
                }
                VoiceState.State.LISTENING_COMMAND -> {
                    // Stop listening
                    stopListening()
                }
                VoiceState.State.SPEAKING_RESPONSE -> {
                    // Stop speaking
                    stopSpeaking()
                }
                else -> {
                    // Do nothing for other states
                }
            }
        }
    }

    fun onSettingsClicked() {
        _uiState.value = _uiState.value.copy(showSettings = true)
    }

    fun onPermissionsGranted() {
        _uiState.value = _uiState.value.copy(
            permissionsGranted = true,
            shouldStartWakeWordService = true
        )
    }

    fun onPermissionsDenied() {
        _uiState.value = _uiState.value.copy(
            permissionsGranted = false,
            showPermissionDialog = true
        )
    }

    fun setMissingPermissions(permissions: List<String>) {
        _uiState.value = _uiState.value.copy(
            missingPermissions = permissions,
            showPermissionDialog = true
        )
    }

    fun onWakeWordToggle(enabled: Boolean) {
        viewModelScope.launch {
            try {
                if (enabled) {
                    voiceRepository.startWakeWordDetection()
                } else {
                    voiceRepository.stopWakeWordDetection()
                }
                
                settingsRepository.setWakeWordEnabled(enabled)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Failed to toggle wake word: ${e.message}"
                )
            }
        }
    }

    fun onWakeWordServiceStarted() {
        _uiState.value = _uiState.value.copy(
            shouldStartWakeWordService = false,
            isWakeWordServiceRunning = true
        )
    }

    fun onVoiceCommandIntent() {
        // Handle voice command from external intent
        viewModelScope.launch {
            startListening()
        }
    }

    fun onWakeWordDetected() {
        // Wake word was detected, voice processing should start automatically
        _uiState.value = _uiState.value.copy(
            lastWakeWordDetection = System.currentTimeMillis()
        )
    }

    fun onResume() {
        // Refresh state when activity resumes
        viewModelScope.launch {
            refreshVoiceState()
        }
    }

    fun onPause() {
        // Handle activity pause
    }

    fun onDestroy() {
        // Cleanup when activity is destroyed
        viewModelScope.launch {
            stopListening()
        }
    }

    fun dismissError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    fun dismissPermissionDialog() {
        _uiState.value = _uiState.value.copy(showPermissionDialog = false)
    }

    private suspend fun startListening() {
        try {
            voiceRepository.startListening()
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Failed to start listening: ${e.message}"
            )
        }
    }

    private suspend fun stopListening() {
        try {
            voiceRepository.stopListening()
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Failed to stop listening: ${e.message}"
            )
        }
    }

    private suspend fun stopSpeaking() {
        try {
            voiceRepository.stopSpeaking()
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Failed to stop speaking: ${e.message}"
            )
        }
    }

    private suspend fun refreshVoiceState() {
        // Refresh the current voice state
        try {
            val currentState = voiceRepository.getVoiceState()
            // State will be updated through the observer
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "Failed to refresh state: ${e.message}"
            )
        }
    }

    private fun getVoiceStateText(voiceState: VoiceState): String {
        return when (voiceState.currentState) {
            VoiceState.State.IDLE -> "Ready to listen"
            VoiceState.State.LISTENING_WAKE_WORD -> "Say \"Hey Zara\" to start"
            VoiceState.State.LISTENING_COMMAND -> "Listening for your command..."
            VoiceState.State.PROCESSING_COMMAND -> "Processing your request..."
            VoiceState.State.GENERATING_RESPONSE -> "Thinking about your request..."
            VoiceState.State.SPEAKING_RESPONSE -> "Zara is responding..."
            VoiceState.State.EXECUTING_ACTION -> "Executing your command..."
            VoiceState.State.ERROR -> voiceState.errorMessage ?: "Something went wrong"
            VoiceState.State.DISABLED -> "Voice assistant is disabled"
        }
    }
}

/**
 * UI state for the main screen
 */
data class MainUiState(
    val voiceState: VoiceState = VoiceState(
        currentState = VoiceState.State.IDLE,
        isWakeWordActive = false,
        isListening = false,
        isProcessing = false,
        isSpeaking = false
    ),
    val voiceStateText: String = "Ready to listen",
    val isWakeWordActive: Boolean = false,
    val isWakeWordEnabled: Boolean = true,
    val wakeWordSensitivity: Float = 0.5f,
    val isWakeWordServiceRunning: Boolean = false,
    val shouldStartWakeWordService: Boolean = false,
    val permissionsGranted: Boolean = false,
    val missingPermissions: List<String> = emptyList(),
    val showPermissionDialog: Boolean = false,
    val showSettings: Boolean = false,
    val errorMessage: String? = null,
    val lastWakeWordDetection: Long? = null,
    val isFirstLaunch: Boolean = false
)
