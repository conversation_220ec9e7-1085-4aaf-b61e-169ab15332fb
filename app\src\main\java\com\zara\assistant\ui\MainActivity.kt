package com.zara.assistant.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.zara.assistant.presentation.theme.ZaraTheme
// Removed PersistentVoskService - using clean Android STT instead
import com.zara.assistant.services.WakeWordService
import com.zara.assistant.ui.navigation.ZaraDestinations
import com.zara.assistant.ui.navigation.ZaraNavigation
import com.zara.assistant.ui.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Main activity for Zara AI Voice Assistant
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val viewModel: MainViewModel by viewModels()

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            viewModel.onPermissionsGranted()
        } else {
            viewModel.onPermissionsDenied()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // API keys are hardcoded in NetworkModule and services

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            ZaraTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainContent()
                }
            }
        }

        // Vosk service removed - using clean Android STT in VoiceProcessingService instead

        // Check and request permissions
        checkPermissions()

        // Handle intent if launched from notification or voice command
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let { handleIntent(it) }
    }

    @Composable
    private fun MainContent() {
        val navController = rememberNavController()
        val uiState by viewModel.uiState.collectAsState()

        LaunchedEffect(uiState.permissionsGranted) {
            if (uiState.permissionsGranted && uiState.shouldStartWakeWordService) {
                startWakeWordService()
            }
        }

        // Determine start destination based on first launch and permissions
        val startDestination = when {
            uiState.isFirstLaunch -> ZaraDestinations.ONBOARDING_ROUTE
            !uiState.permissionsGranted -> ZaraDestinations.PERMISSIONS_ROUTE
            else -> ZaraDestinations.MAIN_ROUTE
        }

        ZaraNavigation(
            mainViewModel = viewModel,
            navController = navController,
            startDestination = startDestination
        )
    }

    private fun checkPermissions() {
        val requiredPermissions = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.POST_NOTIFICATIONS
        )
        
        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isEmpty()) {
            viewModel.onPermissionsGranted()
        } else {
            viewModel.setMissingPermissions(missingPermissions)
        }
    }

    private fun requestPermissions() {
        val requiredPermissions = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.POST_NOTIFICATIONS,
            Manifest.permission.SYSTEM_ALERT_WINDOW,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.SEND_SMS,
            Manifest.permission.READ_CONTACTS,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
        
        permissionLauncher.launch(requiredPermissions)
    }

    private fun startWakeWordService() {
        WakeWordService.startService(this)
        viewModel.onWakeWordServiceStarted()
    }

    private fun handleIntent(intent: Intent) {
        when (intent.action) {
            Intent.ACTION_VOICE_COMMAND -> {
                // Handle voice command intent
                viewModel.onVoiceCommandIntent()
            }
            "com.zara.assistant.WAKE_WORD_DETECTED" -> {
                // Handle wake word detection
                viewModel.onWakeWordDetected()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.onResume()
    }

    override fun onPause() {
        super.onPause()
        viewModel.onPause()
    }

    // Removed startPersistentVoskService - using clean Android STT instead

    override fun onDestroy() {
        super.onDestroy()
        viewModel.onDestroy()

        // Vosk service removed - no cleanup needed for Android STT
    }
}
