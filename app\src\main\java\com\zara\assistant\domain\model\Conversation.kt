package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Represents a conversation between user and <PERSON><PERSON>
 */
@Parcelize
data class Conversation(
    val id: String,
    val startTime: Date,
    val endTime: Date? = null,
    val messages: List<ConversationMessage> = emptyList(),
    val isActive: Boolean = true,
    val summary: String? = null,
    val totalMessages: Int = 0,
    val averageResponseTime: Long = 0L
) : Parcelable

/**
 * Individual message in a conversation
 */
@Parcelize
data class ConversationMessage(
    val id: String,
    val conversationId: String,
    val content: String,
    val timestamp: Date,
    val sender: MessageSender,
    val messageType: MessageType = MessageType.TEXT,
    val metadata: Map<String, String> = emptyMap()
) : Parcelable

/**
 * Message sender types
 */
enum class MessageSender {
    USER,
    ZARA,
    SYSTEM
}

/**
 * Message types
 */
enum class MessageType {
    TEXT,
    VOICE,
    ACTION,
    ERROR,
    SYSTEM_NOTIFICATION
}
