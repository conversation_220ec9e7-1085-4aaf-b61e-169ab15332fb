package com.zara.assistant.domain.repository

import com.zara.assistant.domain.model.Conversation
import com.zara.assistant.domain.model.ConversationMessage
import com.zara.assistant.domain.model.VoiceCommand
import com.zara.assistant.domain.model.AIResponse
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for conversation management
 */
interface ConversationRepository {
    
    /**
     * Conversation management
     */
    suspend fun createConversation(): Result<Conversation>
    suspend fun getActiveConversation(): Conversation?
    suspend fun getConversation(id: String): Conversation?
    suspend fun getAllConversations(): Flow<List<Conversation>>
    suspend fun endConversation(id: String): Result<Unit>
    suspend fun deleteConversation(id: String): Result<Unit>
    suspend fun clearAllConversations(): Result<Unit>
    
    /**
     * Message management
     */
    suspend fun addMessage(
        conversationId: String,
        message: ConversationMessage
    ): Result<Unit>
    
    suspend fun addUserCommand(
        conversationId: String,
        command: VoiceCommand
    ): Result<ConversationMessage>
    
    suspend fun addAIResponse(
        conversationId: String,
        response: AIResponse
    ): Result<ConversationMessage>
    
    suspend fun getMessages(conversationId: String): Flow<List<ConversationMessage>>
    suspend fun getRecentMessages(conversationId: String, limit: Int): List<ConversationMessage>
    suspend fun deleteMessage(messageId: String): Result<Unit>
    
    /**
     * Conversation history
     */
    suspend fun getConversationHistory(conversationId: String): List<String>
    suspend fun getConversationSummary(conversationId: String): String?
    suspend fun updateConversationSummary(conversationId: String, summary: String): Result<Unit>
    
    /**
     * Search and filtering
     */
    suspend fun searchConversations(query: String): List<Conversation>
    suspend fun searchMessages(query: String): List<ConversationMessage>
    suspend fun getConversationsByDateRange(
        startDate: Long,
        endDate: Long
    ): List<Conversation>
    
    /**
     * Statistics
     */
    suspend fun getConversationStats(): ConversationStats
    suspend fun getMessageCount(conversationId: String): Int
    suspend fun getAverageResponseTime(conversationId: String): Long
    
    /**
     * Export and backup
     */
    suspend fun exportConversation(conversationId: String): Result<String>
    suspend fun exportAllConversations(): Result<String>
    suspend fun importConversations(data: String): Result<Unit>
}

/**
 * Conversation statistics
 */
data class ConversationStats(
    val totalConversations: Int,
    val totalMessages: Int,
    val averageConversationLength: Int,
    val averageResponseTime: Long,
    val mostActiveDay: String,
    val totalVoiceCommands: Int,
    val successfulCommands: Int,
    val failedCommands: Int
)
