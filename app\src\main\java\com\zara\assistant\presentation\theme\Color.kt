package com.zara.assistant.presentation.theme

import androidx.compose.ui.graphics.Color

// Primary Brand Colors
val DeepTeal = Color(0xFF006B5C)
val SoftCoral = Color(0xFFFF6B6B)
val LuminousBlue = Color(0xFF4FC3F7)

// Neumorphism Base Colors
val NeuroBackgroundLight = Color(0xFFE6E7EE)
val NeuroBackgroundDark = Color(0xFF2C2C34)

// Light Theme Neumorphism
val NeuroLightShadow = Color(0xFFA3B1C6)
val NeuroLightHighlight = Color(0xFFFFFFFF)
val NeuroSurfaceLight = Color(0xFFE6E7EE)
val NeuroSurfaceElevatedLight = Color(0xFFF0F1F8)

// Dark Theme Neumorphism
val NeuroDarkShadow = Color(0xFF1A1A20)
val NeuroDarkHighlight = Color(0xFF3E3E48)
val NeuroSurfaceDark = Color(0xFF2C2C34)
val NeuroSurfaceElevatedDark = Color(0xFF363640)

// Text Colors
val TextPrimaryLight = Color(0xFF1C1C1E)
val TextSecondaryLight = Color(0xFF6D6D80)
val TextTertiaryLight = Color(0xFF8E8E93)

val TextPrimaryDark = Color(0xFFFFFFFF)
val TextSecondaryDark = Color(0xFFAEAEB2)
val TextTertiaryDark = Color(0xFF8E8E93)

// Accent Colors
val AccentSuccess = Color(0xFF34C759)
val AccentWarning = Color(0xFFFF9500)
val AccentError = Color(0xFFFF3B30)
val AccentInfo = Color(0xFF007AFF)

// Voice Interaction Colors
val VoiceListening = Color(0xFF4FC3F7)
val VoiceProcessing = Color(0xFFFF6B6B)
val VoiceSpeaking = Color(0xFF006B5C)
val VoiceIdle = Color(0xFF8E8E93)

// Gradient Colors
val GradientStart = Color(0xFF4FC3F7)
val GradientMiddle = Color(0xFF006B5C)
val GradientEnd = Color(0xFFFF6B6B)

// Material Design 3 Light Colors
val md_theme_light_primary = Color(0xFF006B5C)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFF7FF8E6)
val md_theme_light_onPrimaryContainer = Color(0xFF00201B)
val md_theme_light_secondary = Color(0xFF4A635F)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFCCE8E2)
val md_theme_light_onSecondaryContainer = Color(0xFF05201C)
val md_theme_light_tertiary = Color(0xFF456179)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFCCE7FF)
val md_theme_light_onTertiaryContainer = Color(0xFF001E31)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFF5FFF9)
val md_theme_light_onBackground = Color(0xFF161D1A)
val md_theme_light_surface = Color(0xFFF5FFF9)
val md_theme_light_onSurface = Color(0xFF161D1A)
val md_theme_light_surfaceVariant = Color(0xFFDAE5E1)
val md_theme_light_onSurfaceVariant = Color(0xFF3F4946)
val md_theme_light_outline = Color(0xFF6F7976)
val md_theme_light_inverseOnSurface = Color(0xFFECF2EE)
val md_theme_light_inverseSurface = Color(0xFF2B322F)
val md_theme_light_inversePrimary = Color(0xFF63DBCA)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = Color(0xFF006B5C)
val md_theme_light_outlineVariant = Color(0xFFBEC9C5)
val md_theme_light_scrim = Color(0xFF000000)

// Material Design 3 Dark Colors
val md_theme_dark_primary = Color(0xFF63DBCA)
val md_theme_dark_onPrimary = Color(0xFF003730)
val md_theme_dark_primaryContainer = Color(0xFF005146)
val md_theme_dark_onPrimaryContainer = Color(0xFF7FF8E6)
val md_theme_dark_secondary = Color(0xFFB0CCC6)
val md_theme_dark_onSecondary = Color(0xFF1C3531)
val md_theme_dark_secondaryContainer = Color(0xFF334B47)
val md_theme_dark_onSecondaryContainer = Color(0xFFCCE8E2)
val md_theme_dark_tertiary = Color(0xFFB0CBE3)
val md_theme_dark_onTertiary = Color(0xFF1B3447)
val md_theme_dark_tertiaryContainer = Color(0xFF324A60)
val md_theme_dark_onTertiaryContainer = Color(0xFFCCE7FF)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF0E1512)
val md_theme_dark_onBackground = Color(0xFFDEE5E1)
val md_theme_dark_surface = Color(0xFF0E1512)
val md_theme_dark_onSurface = Color(0xFFDEE5E1)
val md_theme_dark_surfaceVariant = Color(0xFF3F4946)
val md_theme_dark_onSurfaceVariant = Color(0xFFBEC9C5)
val md_theme_dark_outline = Color(0xFF889390)
val md_theme_dark_inverseOnSurface = Color(0xFF0E1512)
val md_theme_dark_inverseSurface = Color(0xFFDEE5E1)
val md_theme_dark_inversePrimary = Color(0xFF006B5C)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFF63DBCA)
val md_theme_dark_outlineVariant = Color(0xFF3F4946)
val md_theme_dark_scrim = Color(0xFF000000)
