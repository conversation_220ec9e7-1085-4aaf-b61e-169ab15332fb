package com.zara.assistant.data.local.preferences

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.zara.assistant.core.Constants
import com.zara.assistant.domain.model.VoiceSettings
import com.zara.assistant.domain.repository.ThemeMode
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling app preferences using DataStore
 */
@Singleton
class PreferencesManager @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    
    companion object {
        // Voice settings keys
        val WAKE_WORD_ENABLED = booleanPreferencesKey(Constants.Preferences.WAKE_WORD_ENABLED)
        val WAKE_WORD_SENSITIVITY = floatPreferencesKey(Constants.Preferences.WAKE_WORD_SENSITIVITY)
        val SPEECH_RATE = floatPreferencesKey(Constants.Preferences.SPEECH_RATE)
        val SPEECH_PITCH = floatPreferencesKey(Constants.Preferences.SPEECH_PITCH)
        val VOICE_LANGUAGE = stringPreferencesKey(Constants.Preferences.VOICE_LANGUAGE)
        val AUTO_LISTEN = booleanPreferencesKey(Constants.Preferences.AUTO_LISTEN)
        
        // AI settings keys
        val AI_PERSONALITY = stringPreferencesKey(Constants.Preferences.AI_PERSONALITY)
        val AI_RESPONSE_STYLE = stringPreferencesKey(Constants.Preferences.AI_RESPONSE_STYLE)
        
        // Privacy settings keys
        val CONVERSATION_HISTORY_ENABLED = booleanPreferencesKey(Constants.Preferences.CONVERSATION_HISTORY_ENABLED)
        val ANALYTICS_ENABLED = booleanPreferencesKey(Constants.Preferences.ANALYTICS_ENABLED)
        
        // App settings keys
        val FIRST_LAUNCH = booleanPreferencesKey(Constants.Preferences.FIRST_LAUNCH)
        val ACCESSIBILITY_SERVICE_ENABLED = booleanPreferencesKey(Constants.Preferences.ACCESSIBILITY_SERVICE_ENABLED)
        val NOTIFICATION_ACCESS_ENABLED = booleanPreferencesKey(Constants.Preferences.NOTIFICATION_ACCESS_ENABLED)
        
        // Theme settings
        val THEME_MODE = stringPreferencesKey("theme_mode")
    }

    // Voice Settings
    suspend fun setWakeWordEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[WAKE_WORD_ENABLED] = enabled
        }
    }

    fun isWakeWordEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[WAKE_WORD_ENABLED] ?: true
        }
    }

    suspend fun setWakeWordSensitivity(sensitivity: Float) {
        dataStore.edit { preferences ->
            preferences[WAKE_WORD_SENSITIVITY] = sensitivity
        }
    }

    fun getWakeWordSensitivity(): Flow<Float> {
        return dataStore.data.map { preferences ->
            preferences[WAKE_WORD_SENSITIVITY] ?: Constants.WakeWord.DEFAULT_SENSITIVITY
        }
    }

    suspend fun setSpeechRate(rate: Float) {
        dataStore.edit { preferences ->
            preferences[SPEECH_RATE] = rate
        }
    }

    fun getSpeechRate(): Flow<Float> {
        return dataStore.data.map { preferences ->
            preferences[SPEECH_RATE] ?: Constants.Voice.DEFAULT_SPEECH_RATE
        }
    }

    suspend fun setSpeechPitch(pitch: Float) {
        dataStore.edit { preferences ->
            preferences[SPEECH_PITCH] = pitch
        }
    }

    fun getSpeechPitch(): Flow<Float> {
        return dataStore.data.map { preferences ->
            preferences[SPEECH_PITCH] ?: Constants.Voice.DEFAULT_PITCH
        }
    }

    suspend fun setVoiceLanguage(language: String) {
        dataStore.edit { preferences ->
            preferences[VOICE_LANGUAGE] = language
        }
    }

    fun getVoiceLanguage(): Flow<String> {
        return dataStore.data.map { preferences ->
            preferences[VOICE_LANGUAGE] ?: "en-US"
        }
    }

    suspend fun setAutoListen(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[AUTO_LISTEN] = enabled
        }
    }

    fun isAutoListenEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[AUTO_LISTEN] ?: false
        }
    }

    fun getVoiceSettings(): Flow<VoiceSettings> {
        return dataStore.data.map { preferences ->
            VoiceSettings(
                isWakeWordEnabled = preferences[WAKE_WORD_ENABLED] ?: true,
                wakeWordSensitivity = preferences[WAKE_WORD_SENSITIVITY] ?: Constants.WakeWord.DEFAULT_SENSITIVITY,
                speechRate = preferences[SPEECH_RATE] ?: Constants.Voice.DEFAULT_SPEECH_RATE,
                speechPitch = preferences[SPEECH_PITCH] ?: Constants.Voice.DEFAULT_PITCH,
                language = preferences[VOICE_LANGUAGE] ?: "en-US",
                autoListenAfterResponse = preferences[AUTO_LISTEN] ?: false
            )
        }
    }

    // AI Settings
    suspend fun setAIPersonality(personality: Constants.AIPersonality) {
        dataStore.edit { preferences ->
            preferences[AI_PERSONALITY] = personality.name
        }
    }

    fun getAIPersonality(): Flow<Constants.AIPersonality> {
        return dataStore.data.map { preferences ->
            val personalityName = preferences[AI_PERSONALITY] ?: Constants.AIPersonality.FRIENDLY.name
            try {
                Constants.AIPersonality.valueOf(personalityName)
            } catch (e: IllegalArgumentException) {
                Constants.AIPersonality.FRIENDLY
            }
        }
    }

    suspend fun setAIResponseStyle(style: Constants.AIResponseStyle) {
        dataStore.edit { preferences ->
            preferences[AI_RESPONSE_STYLE] = style.name
        }
    }

    fun getAIResponseStyle(): Flow<Constants.AIResponseStyle> {
        return dataStore.data.map { preferences ->
            val styleName = preferences[AI_RESPONSE_STYLE] ?: Constants.AIResponseStyle.CONVERSATIONAL.name
            try {
                Constants.AIResponseStyle.valueOf(styleName)
            } catch (e: IllegalArgumentException) {
                Constants.AIResponseStyle.CONVERSATIONAL
            }
        }
    }

    // Privacy Settings
    suspend fun setConversationHistoryEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[CONVERSATION_HISTORY_ENABLED] = enabled
        }
    }

    fun isConversationHistoryEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[CONVERSATION_HISTORY_ENABLED] ?: true
        }
    }

    suspend fun setAnalyticsEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[ANALYTICS_ENABLED] = enabled
        }
    }

    fun isAnalyticsEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[ANALYTICS_ENABLED] ?: false
        }
    }

    // App Settings
    suspend fun setFirstLaunchCompleted() {
        dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH] = false
        }
    }

    fun isFirstLaunch(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[FIRST_LAUNCH] ?: true
        }
    }

    suspend fun setAccessibilityServiceEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[ACCESSIBILITY_SERVICE_ENABLED] = enabled
        }
    }

    fun isAccessibilityServiceEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[ACCESSIBILITY_SERVICE_ENABLED] ?: false
        }
    }

    suspend fun setNotificationAccessEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[NOTIFICATION_ACCESS_ENABLED] = enabled
        }
    }

    fun isNotificationAccessEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[NOTIFICATION_ACCESS_ENABLED] ?: false
        }
    }

    // Theme Settings
    suspend fun setThemeMode(mode: ThemeMode) {
        dataStore.edit { preferences ->
            preferences[THEME_MODE] = mode.name
        }
    }

    fun getThemeMode(): Flow<ThemeMode> {
        return dataStore.data.map { preferences ->
            val modeName = preferences[THEME_MODE] ?: ThemeMode.SYSTEM.name
            try {
                ThemeMode.valueOf(modeName)
            } catch (e: IllegalArgumentException) {
                ThemeMode.SYSTEM
            }
        }
    }

    // Utility methods
    suspend fun clearAllPreferences() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    suspend fun exportPreferences(): Map<String, Any> {
        val preferences = mutableMapOf<String, Any>()
        dataStore.data.collect { prefs ->
            prefs.asMap().forEach { (key, value) ->
                preferences[key.name] = value
            }
        }
        return preferences
    }
}
