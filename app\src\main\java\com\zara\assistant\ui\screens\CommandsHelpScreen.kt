package com.zara.assistant.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.zara.assistant.presentation.components.NeumorphismCard

/**
 * Screen showing available voice commands and help
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommandsHelpScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val commandCategories = listOf(
        CommandCategory(
            title = "Device Control",
            commands = listOf(
                "Turn on WiFi",
                "Turn off Bluetooth",
                "Set brightness to 50%",
                "Increase volume",
                "Take a screenshot",
                "Enable airplane mode"
            )
        ),
        CommandCategory(
            title = "App Management",
            commands = listOf(
                "Open WhatsApp",
                "Close Chrome",
                "Switch to Spotify",
                "Open Settings",
                "Launch Camera"
            )
        ),
        CommandCategory(
            title = "Communication",
            commands = listOf(
                "Call Mom",
                "Send message to John",
                "Read my notifications",
                "Text Sarah hello"
            )
        ),
        CommandCategory(
            title = "Information",
            commands = listOf(
                "What's the weather like?",
                "Search for restaurants nearby",
                "What time is it?",
                "Tell me the news",
                "How do I cook pasta?"
            )
        ),
        CommandCategory(
            title = "Entertainment",
            commands = listOf(
                "Play music",
                "Next song",
                "Pause",
                "Play my workout playlist",
                "What's this song?"
            )
        ),
        CommandCategory(
            title = "General Conversation",
            commands = listOf(
                "Tell me a joke",
                "How are you?",
                "What can you do?",
                "Help me with math",
                "Set a reminder"
            )
        )
    )

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Voice Commands",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Here are some things you can say to Zara:",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = 4.dp)
                )
            }

            items(commandCategories) { category ->
                CommandCategoryCard(category = category)
            }

            item {
                NeumorphismCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Text(
                            text = "Tips",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        val tips = listOf(
                            "Say \"Hey Zara\" to activate voice control",
                            "Speak clearly and at a normal pace",
                            "You can interrupt Zara while she's speaking",
                            "Try different ways of saying the same thing",
                            "Use natural language - no need for exact commands"
                        )

                        tips.forEach { tip ->
                            Text(
                                text = "• $tip",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
private fun CommandCategoryCard(
    category: CommandCategory,
    modifier: Modifier = Modifier
) {
    NeumorphismCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column {
            Text(
                text = category.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            category.commands.forEach { command ->
                Text(
                    text = "\"$command\"",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

/**
 * Data class for command categories
 */
private data class CommandCategory(
    val title: String,
    val commands: List<String>
)
