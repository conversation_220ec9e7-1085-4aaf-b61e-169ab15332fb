package com.zara.assistant.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Represents an AI response to a user command
 */
@Parcelize
data class AIResponse(
    val id: String,
    val text: String,
    val timestamp: Date,
    val commandId: String,
    val responseTime: Long,
    val source: AISource,
    val confidence: Float,
    val actions: List<SystemAction> = emptyList(),
    val metadata: Map<String, String> = emptyMap()
) : Parcelable

/**
 * AI service sources
 */
enum class AISource {
    COHERE,
    PERPLEXITY,
    LOCAL,
    CACHED
}


