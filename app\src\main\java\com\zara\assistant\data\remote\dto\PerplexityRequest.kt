package com.zara.assistant.data.remote.dto

/**
 * Perplexity API request DTOs
 */
data class PerplexityChatRequest(
    val model: String = "llama-3.1-sonar-small-128k-online",
    val messages: List<PerplexityMessage>,
    val max_tokens: Int = 1000,
    val temperature: Double = 0.7,
    val top_p: Double = 1.0,
    val return_citations: <PERSON>olean = true,
    val search_domain_filter: List<String> = emptyList(),
    val return_images: <PERSON><PERSON>an = false,
    val return_related_questions: <PERSON><PERSON>an = false,
    val search_recency_filter: String = "month",
    val top_k: Int = 0,
    val stream: <PERSON><PERSON>an = false,
    val presence_penalty: Double = 0.0,
    val frequency_penalty: Double = 1.0
)

data class PerplexityMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)
